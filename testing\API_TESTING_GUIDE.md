# ATMA Backend API Testing Guide

## Overview

This guide provides detailed information about testing the ATMA Backend API endpoints using the automated testing suite.

## Test Architecture

### Test Structure
```
testing/
├── config/           # Configuration files
├── data/            # Test data for users
├── utils/           # Utility functions and clients
├── tests/           # Individual test files
└── *.js            # Test runners
```

### Key Components

#### 1. API Client (`utils/api-client.js`)
- Centralized HTTP client with axios
- Automatic token management
- Request/response logging
- Rate limiting protection
- Error handling and retries

#### 2. WebSocket Client (`utils/websocket-client.js`)
- Socket.IO client for real-time testing
- Authentication handling
- Notification management
- Connection state tracking
- Event listening and validation

#### 3. Test Helpers (`utils/helpers.js`)
- Logging utilities with colors
- Response validation functions
- Test result formatting
- Retry mechanisms
- Data validation helpers

## API Endpoints Tested

### Authentication Service (`/api/auth/`)

#### POST /api/auth/register
**Test:** `01-register.test.js`
- Validates user registration
- Checks response structure
- Verifies token generation
- Tests email/username uniqueness

**Request Body Validation:**
```javascript
{
  "email": "<EMAIL>",     // Required, valid email
  "password": "myPassword1",       // Required, min 8 chars
  "username": "johndoe"            // Required, alphanumeric
}
```

#### POST /api/auth/login
**Test:** `02-login.test.js`
- Tests user authentication
- Validates JWT token response
- Checks user data integrity
- Verifies token storage

#### GET /api/auth/profile
**Test:** `02-login.test.js`
- Tests authenticated profile retrieval
- Validates user data structure
- Checks authorization headers

#### PUT /api/auth/profile
**Test:** `04-profile-update.test.js`
- Tests profile updates
- Validates field changes
- Checks data persistence
- Tests partial updates

### Assessment Service (`/api/assessment/`)

#### POST /api/assessment/submit
**Test:** `05-assessment.test.js`
- Validates assessment data structure
- Tests job creation
- Checks queue status
- Validates response format

**Assessment Data Structure:**
```javascript
{
  "assessmentName": "Test Assessment",
  "riasec": {
    "realistic": 75,        // 0-100
    "investigative": 80,    // 0-100
    "artistic": 65,         // 0-100
    "social": 70,          // 0-100
    "enterprising": 85,    // 0-100
    "conventional": 60     // 0-100
  },
  "ocean": {
    "openness": 80,           // 0-100
    "conscientiousness": 75,  // 0-100
    "extraversion": 70,       // 0-100
    "agreeableness": 85,      // 0-100
    "neuroticism": 40         // 0-100
  },
  "viaIs": {
    "creativity": 80,         // 0-100
    "curiosity": 85,          // 0-100
    // ... 21 more character strengths
  }
}
```

#### GET /api/assessment/status/:jobId
**Test:** `05-assessment.test.js`
- Tests job status tracking
- Validates status transitions
- Checks completion detection
- Tests error handling

### Archive Service (`/api/archive/`)

#### GET /api/archive/results
**Test:** `07-profile-persona.test.js`
- Tests result listing
- Validates pagination
- Checks filtering options
- Tests sorting functionality

#### GET /api/archive/results/:resultId
**Test:** `07-profile-persona.test.js`
- Tests detailed result retrieval
- Validates persona profile structure
- Checks assessment data integrity
- Tests career recommendations

### Chatbot Service (`/api/chatbot/`)

#### POST /api/chatbot/conversations
**Test:** `08-chatbot.test.js`
- Tests conversation creation
- Validates context types
- Checks assessment integration
- Tests metadata handling

#### POST /api/chatbot/conversations/:id/messages
**Test:** `08-chatbot.test.js`
- Tests message sending
- Validates AI responses
- Checks conversation flow
- Tests token usage tracking

### Notification Service (WebSocket)

#### WebSocket Connection
**Test:** `03-websocket.test.js`
- Tests connection establishment
- Validates authentication
- Checks reconnection handling
- Tests connection state management

#### Real-time Notifications
**Test:** `06-notifications.test.js`
- Tests analysis-started events
- Tests analysis-complete events
- Validates notification data
- Checks event sequencing

## Test Data Management

### User Data Structure
Each test user has:
- Basic credentials (email, username, password)
- Profile update data
- Assessment data with personality scores
- Chatbot conversation data
- Expected persona characteristics

### Data Validation
- **Email format:** Valid email addresses
- **Password strength:** Minimum 8 characters
- **Score ranges:** 0-100 for personality assessments
- **Required fields:** All mandatory fields present
- **Data types:** Correct types for all fields

## Response Validation

### Standard Response Format
```javascript
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Error Response Format
```javascript
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "details": { /* additional error details */ }
  }
}
```

### Validation Checks
- **Status codes:** Expected HTTP status codes
- **Response structure:** Required fields present
- **Data types:** Correct field types
- **Business logic:** Valid data relationships
- **Security:** No sensitive data exposure

## Performance Testing

### Timing Measurements
- Individual request duration
- End-to-end flow timing
- Assessment processing time
- WebSocket connection time

### Timeout Configuration
- HTTP requests: 30 seconds
- WebSocket auth: 10 seconds
- Assessment processing: 5 minutes
- Retry delays: Exponential backoff

## Security Testing

### Authentication Testing
- Token validation
- Authorization checks
- Session management
- Token expiration handling

### Input Validation
- SQL injection prevention
- XSS protection
- Data sanitization
- Type validation

### Rate Limiting
- Request throttling
- Abuse prevention
- Fair usage policies
- Error handling

## Error Handling

### Network Errors
- Connection timeouts
- Service unavailability
- DNS resolution failures
- SSL/TLS errors

### Application Errors
- Validation failures
- Business logic errors
- Database errors
- External service failures

### Recovery Mechanisms
- Automatic retries
- Exponential backoff
- Circuit breaker patterns
- Graceful degradation

## Best Practices

### Test Design
1. **Isolation:** Each test is independent
2. **Repeatability:** Tests produce consistent results
3. **Cleanup:** Resources are properly cleaned up
4. **Documentation:** Clear test descriptions
5. **Validation:** Comprehensive response checking

### Data Management
1. **Realistic data:** Use representative test data
2. **Edge cases:** Test boundary conditions
3. **Invalid data:** Test error handling
4. **Data cleanup:** Remove test data after tests

### Monitoring
1. **Logging:** Comprehensive test logging
2. **Metrics:** Performance measurements
3. **Alerts:** Failure notifications
4. **Reporting:** Test result summaries

## Integration with CI/CD

### Automated Testing
```bash
# Run in CI pipeline
npm test

# Generate reports
npm test > test-results.log

# Exit codes
# 0 = All tests passed
# 1 = Some tests failed
```

### Test Reports
- Test execution summary
- Individual test results
- Performance metrics
- Error details
- Coverage information
