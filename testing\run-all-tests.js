#!/usr/bin/env node

const { logSuccess, logError, logInfo, createTestSummary, formatDuration } = require('./utils/helpers');
const config = require('./config/test-config');

// Import all test modules
const { runRegistrationTests } = require('./tests/01-register.test');
const { runLoginTests } = require('./tests/02-login.test');
const { runWebSocketTests } = require('./tests/03-websocket.test');
const { runProfileUpdateTests } = require('./tests/04-profile-update.test');
const { runAssessmentTests } = require('./tests/05-assessment.test');
const { runNotificationTests } = require('./tests/06-notifications.test');
const { runProfilePersonaTests } = require('./tests/07-profile-persona.test');
const { runChatbotTests } = require('./tests/08-chatbot.test');
const { runCleanupTests } = require('./tests/09-cleanup.test');

async function runAllTests() {
  const overallStartTime = Date.now();
  
  logSuccess('🚀 Starting ATMA Backend Complete Test Suite');
  logInfo(`📋 Test Configuration:`);
  logInfo(`   - API Base URL: ${config.API_BASE_URL}`);
  logInfo(`   - WebSocket URL: ${config.WEBSOCKET_URL}`);
  logInfo(`   - Test Users: ${Object.keys(config.TEST_USERS).length}`);
  logInfo(`   - Environment: ${config.ENVIRONMENT}`);
  
  const allResults = [];
  let jobIds = {};
  let resultIds = {};
  
  try {
    // Step 1: User Registration
    logSuccess('\n📝 STEP 1: User Registration');
    logInfo('Testing user registration for both test users...');
    const registrationResults = await runRegistrationTests();
    allResults.push(...registrationResults);
    
    if (!registrationResults.every(r => r.success)) {
      throw new Error('Registration tests failed. Cannot proceed with remaining tests.');
    }
    
    // Step 2: User Login
    logSuccess('\n🔐 STEP 2: User Login');
    logInfo('Testing user login and profile retrieval...');
    const loginResults = await runLoginTests();
    allResults.push(...loginResults);
    
    if (!loginResults.every(r => r.success)) {
      throw new Error('Login tests failed. Cannot proceed with remaining tests.');
    }
    
    // Step 3: WebSocket Connection
    logSuccess('\n🔌 STEP 3: WebSocket Connection');
    logInfo('Testing WebSocket connection and authentication...');
    const websocketResults = await runWebSocketTests();
    allResults.push(...websocketResults);
    
    if (!websocketResults.every(r => r.success)) {
      logError('⚠️ WebSocket tests failed. Continuing with remaining tests...');
    }
    
    // Step 4: Profile Update
    logSuccess('\n👤 STEP 4: Profile Update');
    logInfo('Testing user profile updates...');
    const profileUpdateResults = await runProfileUpdateTests();
    allResults.push(...profileUpdateResults);
    
    if (!profileUpdateResults.every(r => r.success)) {
      logError('⚠️ Profile update tests failed. Continuing with remaining tests...');
    }
    
    // Step 5: Assessment Submission
    logSuccess('\n🎯 STEP 5: Assessment Submission');
    logInfo('Testing assessment submission and processing...');
    const assessmentTestResults = await runAssessmentTests();
    allResults.push(...assessmentTestResults.results);
    jobIds = assessmentTestResults.jobIds;
    
    if (!assessmentTestResults.results.every(r => r.success)) {
      logError('⚠️ Assessment tests failed. Some subsequent tests may not work properly...');
    }
    
    // Step 6: WebSocket Notifications
    logSuccess('\n📨 STEP 6: WebSocket Notifications');
    logInfo('Testing WebSocket notifications for assessment completion...');
    const notificationResults = await runNotificationTests(null, jobIds);
    allResults.push(...notificationResults);
    
    if (!notificationResults.every(r => r.success)) {
      logError('⚠️ Notification tests failed. Continuing with remaining tests...');
    }
    
    // Step 7: Profile Persona
    logSuccess('\n🎭 STEP 7: Profile Persona');
    logInfo('Testing assessment results and persona profile retrieval...');
    const personaResults = await runProfilePersonaTests();
    allResults.push(...personaResults);
    
    // Extract result IDs for chatbot tests
    personaResults.forEach(result => {
      if (result.success && result.data && result.data.resultId) {
        if (result.testName.includes('User 1')) {
          resultIds.user1 = result.data.resultId;
        } else if (result.testName.includes('User 2')) {
          resultIds.user2 = result.data.resultId;
        }
      }
    });
    
    if (!personaResults.every(r => r.success)) {
      logError('⚠️ Profile persona tests failed. Continuing with remaining tests...');
    }
    
    // Step 8: Chatbot Testing
    logSuccess('\n🤖 STEP 8: Chatbot Testing');
    logInfo('Testing chatbot conversations with assessment context...');
    const chatbotResults = await runChatbotTests(null, resultIds);
    allResults.push(...chatbotResults);
    
    if (!chatbotResults.every(r => r.success)) {
      logError('⚠️ Chatbot tests failed. Continuing with cleanup...');
    }
    
    // Step 9: Cleanup
    logSuccess('\n🧹 STEP 9: Cleanup');
    logInfo('Testing account deletion and resource cleanup...');
    const cleanupResults = await runCleanupTests();
    allResults.push(...cleanupResults);
    
    if (!cleanupResults.every(r => r.success)) {
      logError('⚠️ Cleanup tests failed. Some resources may not be properly cleaned up.');
    }
    
  } catch (error) {
    logError(`💥 Test suite failed: ${error.message}`);
    
    // Attempt cleanup even if tests failed
    logInfo('🧹 Attempting emergency cleanup...');
    try {
      const emergencyCleanupResults = await runCleanupTests();
      allResults.push(...emergencyCleanupResults);
    } catch (cleanupError) {
      logError(`💥 Emergency cleanup failed: ${cleanupError.message}`);
    }
  }
  
  // Generate final summary
  const overallDuration = Date.now() - overallStartTime;
  const summary = createTestSummary(allResults);
  
  logSuccess('\n📊 FINAL TEST SUMMARY');
  logInfo('═'.repeat(60));
  logInfo(`Total Tests: ${summary.total}`);
  logInfo(`Passed: ${summary.passed}`);
  logInfo(`Failed: ${summary.failed}`);
  logInfo(`Success Rate: ${summary.successRate}`);
  logInfo(`Total Duration: ${formatDuration(overallStartTime)}`);
  logInfo('═'.repeat(60));
  
  // Detailed results by category
  const categories = {
    'Registration': allResults.filter(r => r.testName.includes('Registration')),
    'Login': allResults.filter(r => r.testName.includes('Login') || r.testName.includes('Profile')),
    'WebSocket': allResults.filter(r => r.testName.includes('WebSocket')),
    'Assessment': allResults.filter(r => r.testName.includes('Assessment')),
    'Notifications': allResults.filter(r => r.testName.includes('Notification')),
    'Persona': allResults.filter(r => r.testName.includes('Persona') || r.testName.includes('Results')),
    'Chatbot': allResults.filter(r => r.testName.includes('Chatbot') || r.testName.includes('Conversation')),
    'Cleanup': allResults.filter(r => r.testName.includes('Cleanup') || r.testName.includes('Deletion'))
  };
  
  logInfo('\n📋 Results by Category:');
  Object.entries(categories).forEach(([category, results]) => {
    if (results.length > 0) {
      const passed = results.filter(r => r.success).length;
      const total = results.length;
      const status = passed === total ? '✅' : '❌';
      logInfo(`${status} ${category}: ${passed}/${total}`);
    }
  });
  
  // Failed tests details
  const failedTests = allResults.filter(r => !r.success);
  if (failedTests.length > 0) {
    logError('\n❌ Failed Tests:');
    failedTests.forEach(test => {
      logError(`   - ${test.testName}: ${test.message}`);
    });
  }
  
  // Success message
  if (summary.passed === summary.total) {
    logSuccess('\n🎉 ALL TESTS PASSED! The ATMA backend is working correctly.');
    logSuccess('✅ Both test users completed the full flow successfully.');
    logSuccess('🧹 All resources have been cleaned up.');
  } else {
    logError(`\n💥 ${summary.failed} tests failed. Please check the logs above for details.`);
  }
  
  return {
    success: summary.passed === summary.total,
    summary,
    results: allResults,
    duration: overallDuration
  };
}

// Run if called directly
if (require.main === module) {
  runAllTests()
    .then(result => {
      const exitCode = result.success ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in test suite:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
