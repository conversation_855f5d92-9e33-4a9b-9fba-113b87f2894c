const io = require('socket.io-client');
const config = require('../config/test-config');
const { log, logError, logSuccess, logWarning } = require('./helpers');

class WebSocketClient {
  constructor() {
    this.url = config.WEBSOCKET_URL;
    this.options = config.WEBSOCKET_OPTIONS;
    this.connections = {}; // Store connections for each user
    this.notifications = {}; // Store notifications for each user
  }
  
  // Connect user to WebSocket
  async connect(userKey, token) {
    return new Promise((resolve, reject) => {
      log(`🔌 Connecting ${userKey} to WebSocket at ${this.url}`);
      
      // Create socket connection
      const socket = io(this.url, this.options);
      
      // Store connection
      this.connections[userKey] = socket;
      this.notifications[userKey] = [];
      
      // Connection event handlers
      socket.on('connect', () => {
        log(`✅ ${userKey} connected to WebSocket`);
        
        // Authenticate with JWT token
        log(`🔐 Authenticating ${userKey}...`);
        socket.emit('authenticate', { token });
      });
      
      socket.on('authenticated', (data) => {
        logSuccess(`🎉 ${userKey} authenticated successfully:`, data);
        resolve(socket);
      });
      
      socket.on('auth_error', (error) => {
        logError(`❌ ${userKey} authentication failed:`, error);
        reject(new Error(`Authentication failed: ${error.message}`));
      });
      
      socket.on('disconnect', (reason) => {
        logWarning(`🔌 ${userKey} disconnected:`, reason);
      });
      
      socket.on('connect_error', (error) => {
        logError(`❌ ${userKey} connection error:`, error);
        reject(error);
      });
      
      // Analysis event handlers
      socket.on('analysis-started', (data) => {
        logSuccess(`🚀 ${userKey} received analysis-started:`, data);
        this.notifications[userKey].push({
          type: 'analysis-started',
          data,
          timestamp: new Date().toISOString()
        });
      });
      
      socket.on('analysis-complete', (data) => {
        logSuccess(`🎯 ${userKey} received analysis-complete:`, data);
        this.notifications[userKey].push({
          type: 'analysis-complete',
          data,
          timestamp: new Date().toISOString()
        });
      });
      
      socket.on('analysis-failed', (data) => {
        logError(`💥 ${userKey} received analysis-failed:`, data);
        this.notifications[userKey].push({
          type: 'analysis-failed',
          data,
          timestamp: new Date().toISOString()
        });
      });
      
      // Set authentication timeout
      setTimeout(() => {
        if (!socket.connected || !this.isAuthenticated(userKey)) {
          reject(new Error(`Authentication timeout for ${userKey}`));
        }
      }, config.WEBSOCKET_TIMEOUT);
      
      // Connect to server
      socket.connect();
    });
  }
  
  // Check if user is authenticated
  isAuthenticated(userKey) {
    const socket = this.connections[userKey];
    return socket && socket.connected;
  }
  
  // Wait for specific notification type
  async waitForNotification(userKey, notificationType, timeout = config.ASSESSMENT_PROCESSING_TIMEOUT) {
    return new Promise((resolve, reject) => {
      log(`⏳ Waiting for ${notificationType} notification for ${userKey}...`);
      
      const startTime = Date.now();
      
      const checkNotification = () => {
        const notifications = this.notifications[userKey] || [];
        const notification = notifications.find(n => n.type === notificationType);
        
        if (notification) {
          logSuccess(`📨 Found ${notificationType} notification for ${userKey}`);
          resolve(notification);
          return;
        }
        
        // Check timeout
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Timeout waiting for ${notificationType} notification for ${userKey}`));
          return;
        }
        
        // Check again in 1 second
        setTimeout(checkNotification, 1000);
      };
      
      checkNotification();
    });
  }
  
  // Wait for analysis completion (started -> complete)
  async waitForAnalysisCompletion(userKey, jobId) {
    log(`🔄 Waiting for analysis completion for ${userKey} (jobId: ${jobId})`);
    
    try {
      // Wait for analysis-started
      const startedNotification = await this.waitForNotification(userKey, 'analysis-started', 30000);
      log(`✅ Analysis started for ${userKey}`);
      
      // Wait for analysis-complete
      const completedNotification = await this.waitForNotification(userKey, 'analysis-complete');
      log(`✅ Analysis completed for ${userKey}`);
      
      return {
        started: startedNotification,
        completed: completedNotification
      };
    } catch (error) {
      // Check if analysis failed
      const failedNotification = this.notifications[userKey]?.find(n => n.type === 'analysis-failed');
      if (failedNotification) {
        throw new Error(`Analysis failed for ${userKey}: ${failedNotification.data.error}`);
      }
      throw error;
    }
  }
  
  // Get all notifications for user
  getNotifications(userKey) {
    return this.notifications[userKey] || [];
  }
  
  // Clear notifications for user
  clearNotifications(userKey) {
    this.notifications[userKey] = [];
    log(`🧹 Cleared notifications for ${userKey}`);
  }
  
  // Disconnect user
  disconnect(userKey) {
    const socket = this.connections[userKey];
    if (socket) {
      socket.disconnect();
      delete this.connections[userKey];
      log(`🔌 Disconnected ${userKey} from WebSocket`);
    }
  }
  
  // Disconnect all users
  disconnectAll() {
    Object.keys(this.connections).forEach(userKey => {
      this.disconnect(userKey);
    });
    log(`🔌 Disconnected all users from WebSocket`);
  }
  
  // Get connection status
  getConnectionStatus(userKey) {
    const socket = this.connections[userKey];
    if (!socket) return 'not_connected';
    if (!socket.connected) return 'disconnected';
    return 'connected';
  }
  
  // Test connection
  async testConnection(userKey, token) {
    try {
      await this.connect(userKey, token);
      const status = this.getConnectionStatus(userKey);
      log(`🧪 Connection test for ${userKey}: ${status}`);
      return status === 'connected';
    } catch (error) {
      logError(`🧪 Connection test failed for ${userKey}:`, error.message);
      return false;
    }
  }
}

module.exports = new WebSocketClient();
