const apiClient = require('../utils/api-client');
const websocketClient = require('../utils/websocket-client');
const { logSuccess, logError, formatTestResult, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testWebSocketConnection(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} WebSocket Connection`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Get token from API client (should be set from login test)
    const token = apiClient.getToken(userKey);
    if (!token) {
      throw new Error(`No token found for ${userKey}. Please run login test first.`);
    }
    
    // Test WebSocket connection and authentication
    await websocketClient.connect(userKey, token);
    
    // Verify connection status
    const status = websocketClient.getConnectionStatus(userKey);
    if (status !== 'connected') {
      throw new Error(`Expected connection status 'connected', got '${status}'`);
    }
    
    // Verify authentication
    if (!websocketClient.isAuthenticated(userKey)) {
      throw new Error('User is not authenticated');
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'WebSocket connected and authenticated', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'WebSocket connected and authenticated',
      data: {
        userKey,
        status,
        authenticated: true
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function testWebSocketReconnection(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} WebSocket Reconnection`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Disconnect user
    websocketClient.disconnect(userKey);
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Verify disconnection
    let status = websocketClient.getConnectionStatus(userKey);
    if (status === 'connected') {
      throw new Error('User should be disconnected');
    }
    
    // Get token and reconnect
    const token = apiClient.getToken(userKey);
    if (!token) {
      throw new Error(`No token found for ${userKey}`);
    }
    
    // Reconnect
    await websocketClient.connect(userKey, token);
    
    // Verify reconnection
    status = websocketClient.getConnectionStatus(userKey);
    if (status !== 'connected') {
      throw new Error(`Expected connection status 'connected', got '${status}'`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'WebSocket reconnected successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'WebSocket reconnected successfully',
      data: {
        userKey,
        status,
        authenticated: websocketClient.isAuthenticated(userKey)
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function testWebSocketNotificationSetup(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} WebSocket Notification Setup`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Clear any existing notifications
    websocketClient.clearNotifications(userKey);
    
    // Verify notifications are cleared
    const notifications = websocketClient.getNotifications(userKey);
    if (notifications.length > 0) {
      throw new Error('Notifications should be cleared');
    }
    
    // Verify connection is still active
    const status = websocketClient.getConnectionStatus(userKey);
    if (status !== 'connected') {
      throw new Error(`WebSocket should be connected, status: ${status}`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'WebSocket notification setup complete', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'WebSocket notification setup complete',
      data: {
        userKey,
        notificationCount: notifications.length,
        status
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function runWebSocketTests(userNumber = null) {
  logSuccess('🔌 Starting WebSocket Connection Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('🔌 Testing User 1 WebSocket Connection...');
    const user1ConnectionResult = await testWebSocketConnection(user1Data, 1);
    results.push(user1ConnectionResult);
    
    if (user1ConnectionResult.success) {
      logSuccess('🔄 Testing User 1 WebSocket Reconnection...');
      const user1ReconnectionResult = await testWebSocketReconnection(user1Data, 1);
      results.push(user1ReconnectionResult);
      
      if (user1ReconnectionResult.success) {
        logSuccess('📨 Testing User 1 WebSocket Notification Setup...');
        const user1NotificationResult = await testWebSocketNotificationSetup(user1Data, 1);
        results.push(user1NotificationResult);
      }
    }
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('🔌 Testing User 2 WebSocket Connection...');
    const user2ConnectionResult = await testWebSocketConnection(user2Data, 2);
    results.push(user2ConnectionResult);
    
    if (user2ConnectionResult.success) {
      logSuccess('🔄 Testing User 2 WebSocket Reconnection...');
      const user2ReconnectionResult = await testWebSocketReconnection(user2Data, 2);
      results.push(user2ReconnectionResult);
      
      if (user2ReconnectionResult.success) {
        logSuccess('📨 Testing User 2 WebSocket Notification Setup...');
        const user2NotificationResult = await testWebSocketNotificationSetup(user2Data, 2);
        results.push(user2NotificationResult);
      }
    }
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All WebSocket tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some WebSocket tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testWebSocketConnection,
  testWebSocketReconnection,
  testWebSocketNotificationSetup,
  runWebSocketTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  
  runWebSocketTests(userNumber)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in WebSocket tests:', error);
      process.exit(1);
    });
}
