{"name": "atma-backend-testing", "version": "1.0.0", "description": "Testing suite for ATMA Backend services", "main": "run-all-tests.js", "scripts": {"test": "node run-all-tests.js", "test:single": "node run-single-test.js", "test:step": "node run-step-by-step.js", "test:register": "node run-single-test.js 01-register", "test:login": "node run-single-test.js 02-login", "test:websocket": "node run-single-test.js 03-websocket", "test:profile": "node run-single-test.js 04-profile-update", "test:assessment": "node run-single-test.js 05-assessment", "test:notifications": "node run-single-test.js 06-notifications", "test:persona": "node run-single-test.js 07-profile-persona", "test:chatbot": "node run-single-test.js 08-chatbot", "test:cleanup": "node run-single-test.js 09-cleanup", "test:user1": "node run-all-tests.js 1", "test:user2": "node run-all-tests.js 2"}, "dependencies": {"axios": "^1.6.0", "socket.io-client": "^4.7.2", "colors": "^1.4.0"}, "devDependencies": {}, "keywords": ["testing", "api", "websocket", "atma", "backend"], "author": "ATMA Team", "license": "MIT"}