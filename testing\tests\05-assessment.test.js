const apiClient = require('../utils/api-client');
const { logSuccess, logError, formatTestResult, validateResponse, validateAssessmentData, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testAssessmentSubmission(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Assessment Submission`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Validate assessment data structure before submission
    const assessmentData = userData.assessmentData;
    const validationErrors = validateAssessmentData(assessmentData);
    if (validationErrors.length > 0) {
      throw new Error(`Assessment data validation failed: ${validationErrors.join(', ')}`);
    }
    
    // Submit assessment
    const response = await apiClient.submitAssessment(userKey, assessmentData);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.ASSESSMENT_SUBMIT_SUCCESS, [
      'data.jobId',
      'data.status',
      'data.estimatedProcessingTime',
      'message'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    // Additional validations
    const responseData = response.data.data;
    
    if (!responseData.jobId || !responseData.jobId.startsWith('job_')) {
      throw new Error(`Invalid jobId format: ${responseData.jobId}`);
    }
    
    if (responseData.status !== 'queued') {
      throw new Error(`Expected status 'queued', got '${responseData.status}'`);
    }
    
    if (!responseData.estimatedProcessingTime) {
      throw new Error('Estimated processing time not provided');
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Assessment submitted successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Assessment submitted successfully',
      data: {
        jobId: responseData.jobId,
        status: responseData.status,
        estimatedProcessingTime: responseData.estimatedProcessingTime,
        queuePosition: responseData.queuePosition,
        assessmentName: assessmentData.assessmentName
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function testAssessmentStatusCheck(userKey, jobId, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Assessment Status Check`;
  
  try {
    // Check assessment status
    const response = await apiClient.getAssessmentStatus(userKey, jobId);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.ASSESSMENT_STATUS_SUCCESS, [
      'data.jobId',
      'data.status'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    const responseData = response.data.data;
    
    // Validate jobId matches
    if (responseData.jobId !== jobId) {
      throw new Error(`JobId mismatch: expected ${jobId}, got ${responseData.jobId}`);
    }
    
    // Validate status is valid
    const validStatuses = ['queued', 'processing', 'completed', 'failed'];
    if (!validStatuses.includes(responseData.status)) {
      throw new Error(`Invalid status: ${responseData.status}`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, `Assessment status: ${responseData.status}`, duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: `Assessment status: ${responseData.status}`,
      data: {
        jobId: responseData.jobId,
        status: responseData.status,
        progress: responseData.progress,
        resultId: responseData.resultId
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function waitForAssessmentCompletion(userKey, jobId, userNumber, maxWaitTime = config.ASSESSMENT_PROCESSING_TIMEOUT) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Assessment Completion Wait`;
  
  try {
    logSuccess(`⏳ Waiting for assessment completion (jobId: ${jobId})...`);
    
    let lastStatus = 'unknown';
    let resultId = null;
    
    while (Date.now() - startTime < maxWaitTime) {
      const statusResult = await testAssessmentStatusCheck(userKey, jobId, userNumber);
      
      if (!statusResult.success) {
        throw new Error(`Status check failed: ${statusResult.message}`);
      }
      
      lastStatus = statusResult.data.status;
      resultId = statusResult.data.resultId;
      
      if (lastStatus === 'completed') {
        const duration = Date.now() - startTime;
        const result = formatTestResult(testName, true, 'Assessment completed successfully', duration);
        logSuccess(result);
        
        return {
          success: true,
          testName,
          duration,
          message: 'Assessment completed successfully',
          data: {
            jobId,
            status: lastStatus,
            resultId,
            processingTime: duration
          }
        };
      }
      
      if (lastStatus === 'failed') {
        throw new Error('Assessment processing failed');
      }
      
      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    throw new Error(`Assessment did not complete within ${maxWaitTime}ms. Last status: ${lastStatus}`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message,
      data: {
        jobId,
        lastStatus,
        resultId
      }
    };
  }
}

async function runAssessmentTests(userNumber = null) {
  logSuccess('🎯 Starting Assessment Submission Tests');
  
  const results = [];
  const jobIds = {}; // Store jobIds for later use
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('📝 Testing User 1 Assessment Submission...');
    const user1SubmissionResult = await testAssessmentSubmission(user1Data, 1);
    results.push(user1SubmissionResult);
    
    if (user1SubmissionResult.success) {
      const jobId = user1SubmissionResult.data.jobId;
      jobIds.user1 = jobId;
      
      logSuccess('⏳ Testing User 1 Assessment Completion...');
      const user1CompletionResult = await waitForAssessmentCompletion(
        getUserKey(user1Data.userData.email), 
        jobId, 
        1
      );
      results.push(user1CompletionResult);
    }
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('📝 Testing User 2 Assessment Submission...');
    const user2SubmissionResult = await testAssessmentSubmission(user2Data, 2);
    results.push(user2SubmissionResult);
    
    if (user2SubmissionResult.success) {
      const jobId = user2SubmissionResult.data.jobId;
      jobIds.user2 = jobId;
      
      logSuccess('⏳ Testing User 2 Assessment Completion...');
      const user2CompletionResult = await waitForAssessmentCompletion(
        getUserKey(user2Data.userData.email), 
        jobId, 
        2
      );
      results.push(user2CompletionResult);
    }
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All assessment tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some assessment tests failed (${passed}/${total})`);
  }
  
  return { results, jobIds };
}

// Export for use in other modules
module.exports = {
  testAssessmentSubmission,
  testAssessmentStatusCheck,
  waitForAssessmentCompletion,
  runAssessmentTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  
  runAssessmentTests(userNumber)
    .then(({ results }) => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in assessment tests:', error);
      process.exit(1);
    });
}
