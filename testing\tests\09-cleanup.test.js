const apiClient = require('../utils/api-client');
const websocketClient = require('../utils/websocket-client');
const { logSuccess, logError, formatTestResult, validateResponse, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testDeleteUserAccount(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Account Deletion`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // First, verify user exists by getting profile
    try {
      const profileResponse = await apiClient.getProfile(userKey);
      if (!profileResponse.data.success) {
        throw new Error('User profile not found before deletion');
      }
      logSuccess(`✅ User ${userNumber} profile verified before deletion`);
    } catch (error) {
      throw new Error(`Failed to verify user profile before deletion: ${error.message}`);
    }
    
    // Delete user account
    const response = await apiClient.deleteAccount(userKey);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.DELETE_SUCCESS, [
      'success',
      'message'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    // Verify account is actually deleted by trying to get profile
    try {
      await apiClient.getProfile(userKey);
      throw new Error('User profile should not be accessible after deletion');
    } catch (error) {
      if (error.response && (error.response.status === 401 || error.response.status === 404)) {
        logSuccess(`✅ User ${userNumber} profile correctly inaccessible after deletion`);
      } else {
        throw new Error(`Unexpected error when verifying deletion: ${error.message}`);
      }
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'User account deleted successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'User account deleted successfully',
      data: {
        userKey,
        userNumber,
        email: userData.userData.email,
        username: userData.userData.username
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function testWebSocketCleanup(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} WebSocket Cleanup`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Check if WebSocket connection exists
    const status = websocketClient.getConnectionStatus(userKey);
    
    if (status === 'not_connected') {
      logSuccess(`✅ User ${userNumber} WebSocket already disconnected`);
    } else {
      // Disconnect WebSocket
      websocketClient.disconnect(userKey);
      
      // Verify disconnection
      const newStatus = websocketClient.getConnectionStatus(userKey);
      if (newStatus !== 'not_connected') {
        throw new Error(`WebSocket should be disconnected, status: ${newStatus}`);
      }
      
      logSuccess(`✅ User ${userNumber} WebSocket disconnected successfully`);
    }
    
    // Clear notifications
    websocketClient.clearNotifications(userKey);
    const notifications = websocketClient.getNotifications(userKey);
    
    if (notifications.length > 0) {
      throw new Error(`Notifications should be cleared, found ${notifications.length}`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'WebSocket cleanup completed', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'WebSocket cleanup completed',
      data: {
        userKey,
        finalStatus: websocketClient.getConnectionStatus(userKey),
        notificationsCleared: true
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function testTokenCleanup(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Token Cleanup`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Check if token exists
    const token = apiClient.getToken(userKey);
    
    if (!token) {
      logSuccess(`✅ User ${userNumber} token already cleared`);
    } else {
      // Clear token from API client
      apiClient.setToken(userKey, null);
      
      // Verify token is cleared
      const clearedToken = apiClient.getToken(userKey);
      if (clearedToken) {
        throw new Error('Token should be cleared from API client');
      }
      
      logSuccess(`✅ User ${userNumber} token cleared from API client`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Token cleanup completed', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Token cleanup completed',
      data: {
        userKey,
        tokenCleared: true
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function testCompleteCleanup() {
  const startTime = Date.now();
  const testName = 'Complete System Cleanup';
  
  try {
    // Disconnect all WebSocket connections
    websocketClient.disconnectAll();
    
    // Clear all tokens from API client
    const userKeys = ['testuser1', 'testuser2'];
    userKeys.forEach(userKey => {
      apiClient.setToken(userKey, null);
    });
    
    logSuccess('✅ All WebSocket connections disconnected');
    logSuccess('✅ All tokens cleared from API client');
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Complete system cleanup completed', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Complete system cleanup completed',
      data: {
        websocketConnectionsCleared: true,
        tokensCleared: true
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function runCleanupTests(userNumber = null) {
  logSuccess('🧹 Starting Cleanup Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('🧹 Testing User 1 WebSocket Cleanup...');
    const user1WebSocketResult = await testWebSocketCleanup(user1Data, 1);
    results.push(user1WebSocketResult);
    
    logSuccess('🔑 Testing User 1 Token Cleanup...');
    const user1TokenResult = await testTokenCleanup(user1Data, 1);
    results.push(user1TokenResult);
    
    logSuccess('🗑️ Testing User 1 Account Deletion...');
    const user1DeleteResult = await testDeleteUserAccount(user1Data, 1);
    results.push(user1DeleteResult);
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('🧹 Testing User 2 WebSocket Cleanup...');
    const user2WebSocketResult = await testWebSocketCleanup(user2Data, 2);
    results.push(user2WebSocketResult);
    
    logSuccess('🔑 Testing User 2 Token Cleanup...');
    const user2TokenResult = await testTokenCleanup(user2Data, 2);
    results.push(user2TokenResult);
    
    logSuccess('🗑️ Testing User 2 Account Deletion...');
    const user2DeleteResult = await testDeleteUserAccount(user2Data, 2);
    results.push(user2DeleteResult);
  }
  
  // Complete system cleanup
  logSuccess('🧹 Testing Complete System Cleanup...');
  const completeCleanupResult = await testCompleteCleanup();
  results.push(completeCleanupResult);
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All cleanup tests passed (${passed}/${total})`);
    logSuccess('🎉 All test users and resources have been cleaned up successfully!');
  } else {
    logError(`❌ Some cleanup tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testDeleteUserAccount,
  testWebSocketCleanup,
  testTokenCleanup,
  testCompleteCleanup,
  runCleanupTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  
  runCleanupTests(userNumber)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in cleanup tests:', error);
      process.exit(1);
    });
}
