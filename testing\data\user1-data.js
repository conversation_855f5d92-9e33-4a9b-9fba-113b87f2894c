const config = require('../config/test-config');

module.exports = {
  // Basic user data
  userData: {
    email: config.TEST_USERS.user1.email,
    username: config.TEST_USERS.user1.username,
    password: config.TEST_USERS.user1.password
  },
  
  // Profile update data
  profileUpdate: {
    username: 'testuser1_updated',
    full_name: 'Test User One Updated',
    email: config.TEST_USERS.user1.email // Keep same email
  },
  
  // Assessment data with slight variations for user 1
  assessmentData: {
    assessmentName: "User 1 Assessment Test",
    riasec: {
      realistic: 78,
      investigative: 85,
      artistic: 62,
      social: 75,
      enterprising: 88,
      conventional: 58
    },
    ocean: {
      openness: 85,
      conscientiousness: 78,
      extraversion: 72,
      agreeableness: 88,
      neuroticism: 38
    },
    viaIs: {
      creativity: 85,
      curiosity: 88,
      judgment: 78,
      loveOfLearning: 92,
      perspective: 72,
      bravery: 68,
      perseverance: 82,
      honesty: 88,
      zest: 78,
      love: 82,
      kindness: 88,
      socialIntelligence: 78,
      teamwork: 82,
      fairness: 88,
      leadership: 72,
      forgiveness: 78,
      humility: 82,
      prudence: 78,
      selfRegulation: 82,
      appreciationOfBeauty: 72,
      gratitude: 88,
      hope: 82,
      humor: 78,
      spirituality: 62
    }
  },
  
  // Chatbot conversation data
  chatbotData: {
    conversationTitle: "User 1 Career Guidance Chat",
    contextType: "assessment",
    messages: [
      "Hello! I just completed my assessment and would like career guidance.",
      "Based on my results, what career paths would you recommend?",
      "I'm particularly interested in technology and innovation fields.",
      "How can I leverage my analytical thinking strengths?",
      "What skills should I develop to improve my leadership abilities?"
    ]
  },
  
  // Expected persona characteristics for user 1
  expectedPersona: {
    expectedArchetypes: [
      "The Innovator",
      "The Analyst", 
      "The Strategist",
      "The Problem Solver"
    ],
    expectedStrengths: [
      "Analytical Thinking",
      "Problem Solving",
      "Innovation",
      "Curiosity",
      "Learning"
    ],
    expectedCareers: [
      "Software Engineer",
      "Data Scientist",
      "Research Scientist",
      "Product Manager",
      "Technology Consultant"
    ]
  },
  
  // Test metadata
  metadata: {
    userNumber: 1,
    testDescription: "Primary test user with high investigative and enterprising scores",
    expectedProcessingTime: "2-5 minutes",
    priority: "high"
  }
};
