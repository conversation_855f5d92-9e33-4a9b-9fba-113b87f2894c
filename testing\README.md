# ATMA Backend Testing Suite

## Overview

Testing suite untuk menguji flow lengkap 2 user dari register hingga delete account dengan skenario:

1. **User Registration** - Mendaftar 2 user baru
2. **User Login** - Login kedua user
3. **WebSocket Connection** - Connect ke notification service
4. **Profile Update** - Update profil user
5. **Assessment Submission** - Submit assessment data
6. **WebSocket Notification** - Tunggu notifikasi dari WebSocket
7. **Profile Persona** - Lihat hasil assessment
8. **Chatbot Testing** - Test chatbot dengan assessment context
9. **Account Deletion** - Hapus kedua account

## Prerequisites

1. Semua services harus berjalan:
   - API Gateway (port 3000)
   - Auth Service (port 3001)
   - Assessment Service (port 3002)
   - Archive Service (port 3003)
   - Notification Service (port 3005)
   - Chatbot Service (port 3006)

2. Database harus tersedia dan ter-migrate

3. Install dependencies:
   ```bash
   cd testing
   npm install
   ```

## Installation

1. Navigate to testing directory:
   ```bash
   cd testing
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Verify configuration in `config/test-config.js`:
   - Check API_BASE_URL (default: http://localhost:3000/api)
   - Check WEBSOCKET_URL (default: http://localhost:3000)
   - Modify test user credentials if needed

4. Ensure all ATMA services are running:
   ```bash
   # Check service health
   curl http://localhost:3000/api/health
   ```

## Test Structure

```
testing/
├── README.md
├── package.json
├── config/
│   └── test-config.js
├── data/
│   ├── user1-data.js
│   └── user2-data.js
├── utils/
│   ├── api-client.js
│   ├── websocket-client.js
│   └── helpers.js
├── tests/
│   ├── 01-register.test.js
│   ├── 02-login.test.js
│   ├── 03-websocket.test.js
│   ├── 04-profile-update.test.js
│   ├── 05-assessment.test.js
│   ├── 06-notifications.test.js
│   ├── 07-profile-persona.test.js
│   ├── 08-chatbot.test.js
│   └── 09-cleanup.test.js
├── run-all-tests.js
└── run-single-test.js
```

## Running Tests

### Run All Tests (Recommended)
```bash
# Run complete test suite for both users
node run-all-tests.js

# Or using npm script
npm test
```

### Run Single Test
```bash
# Run specific test for both users
node run-single-test.js <test-name>

# Examples:
node run-single-test.js 01-register
node run-single-test.js 05-assessment
node run-single-test.js 08-chatbot
```

### Run Specific User Test
```bash
# Run test for specific user only
node run-single-test.js <test-name> <user-number>

# Examples:
node run-single-test.js 02-login 1        # Test user 1 login only
node run-single-test.js 03-websocket 2    # Test user 2 websocket only
```

### Run Tests with Additional Parameters
```bash
# For tests that need additional data (like job IDs)
node run-single-test.js 06-notifications null '{"user1":"job_123","user2":"job_456"}'
node run-single-test.js 08-chatbot null '{"user1":"result_123","user2":"result_456"}'
```

### Using NPM Scripts
```bash
npm run test:register      # Registration tests
npm run test:login         # Login tests
npm run test:websocket     # WebSocket tests
npm run test:profile       # Profile update tests
npm run test:assessment    # Assessment tests
npm run test:notifications # Notification tests
npm run test:persona       # Persona profile tests
npm run test:chatbot       # Chatbot tests
npm run test:cleanup       # Cleanup tests
```

## Test Data

### User 1 Data
- Email: `<EMAIL>`
- Username: `testuser1`
- Password: `TestPassword123!`

### User 2 Data
- Email: `<EMAIL>`
- Username: `testuser2`
- Password: `TestPassword123!`

## API Endpoints Tested

- `POST /api/auth/register`
- `POST /api/auth/login`
- `GET /api/auth/profile`
- `PUT /api/auth/profile`
- `POST /api/assessment/submit`
- `GET /api/assessment/status/:jobId`
- `GET /api/archive/results`
- `GET /api/archive/results/:resultId`
- `POST /api/chatbot/conversations`
- `POST /api/chatbot/conversations/:id/messages`
- `DELETE /api/auth/profile` (account deletion)

## WebSocket Events Tested

- Connection to `http://localhost:3000`
- Authentication with JWT token
- `analysis-started` event
- `analysis-complete` event
- `analysis-failed` event

## Expected Flow

1. **Register** → Success (201) with user data and token
2. **Login** → Success (200) with user data and token
3. **WebSocket** → Connected and authenticated
4. **Profile Update** → Success (200) with updated profile
5. **Assessment Submit** → Success (202) with jobId
6. **WebSocket Notification** → Receive analysis-started, then analysis-complete
7. **Profile Persona** → Success (200) with persona_profile data
8. **Chatbot** → Success (201) conversation created, messages exchanged
9. **Cleanup** → Success (200) account deleted

## Error Handling

Tests include error handling for:
- Network errors
- Authentication failures
- Validation errors
- WebSocket connection issues
- Rate limiting
- Service unavailability

## Logging

All tests generate detailed logs:
- Request/Response data
- WebSocket events
- Timing information
- Error details
- Success confirmations

## Configuration

Edit `config/test-config.js` to modify:
- Base URLs
- Timeout values
- Retry attempts
- Log levels
