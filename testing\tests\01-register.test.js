const apiClient = require('../utils/api-client');
const { logSuccess, logError, formatTestResult, validateResponse, formatDuration } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testUserRegistration(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Registration`;
  
  try {
    // Test user registration
    const response = await apiClient.register(userData.userData);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.REGISTER_SUCCESS, [
      'data.user.id',
      'data.user.email',
      'data.user.username',
      'data.user.user_type',
      'data.user.is_active',
      'data.user.token_balance',
      'data.token',
      'message'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    // Additional validations
    const user = response.data.data.user;
    if (user.email !== userData.userData.email) {
      throw new Error(`Email mismatch: expected ${userData.userData.email}, got ${user.email}`);
    }
    
    if (user.username !== userData.userData.username) {
      throw new Error(`Username mismatch: expected ${userData.userData.username}, got ${user.username}`);
    }
    
    if (user.user_type !== 'user') {
      throw new Error(`User type should be 'user', got ${user.user_type}`);
    }
    
    if (!user.is_active) {
      throw new Error('User should be active after registration');
    }
    
    if (typeof user.token_balance !== 'number' || user.token_balance < 0) {
      throw new Error(`Invalid token balance: ${user.token_balance}`);
    }
    
    if (!response.data.data.token) {
      throw new Error('JWT token not provided in response');
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'User registered successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'User registered successfully',
      data: {
        userId: user.id,
        email: user.email,
        username: user.username,
        tokenBalance: user.token_balance,
        token: response.data.data.token
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function runRegistrationTests(userNumber = null) {
  logSuccess('🚀 Starting User Registration Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('📝 Testing User 1 Registration...');
    const user1Result = await testUserRegistration(user1Data, 1);
    results.push(user1Result);
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('📝 Testing User 2 Registration...');
    const user2Result = await testUserRegistration(user2Data, 2);
    results.push(user2Result);
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All registration tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some registration tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testUserRegistration,
  runRegistrationTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  
  runRegistrationTests(userNumber)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in registration tests:', error);
      process.exit(1);
    });
}
