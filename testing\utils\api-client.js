const axios = require('axios');
const config = require('../config/test-config');
const { log, logError, logSuccess } = require('./helpers');

class ApiClient {
  constructor() {
    this.baseURL = config.API_BASE_URL;
    this.timeout = config.HTTP_TIMEOUT;
    this.tokens = {}; // Store tokens for each user
    
    // Create axios instance with default config
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        log(`🚀 ${config.method.toUpperCase()} ${config.url}`);
        if (config.data) {
          log(`📤 Request Body:`, config.data);
        }
        return config;
      },
      (error) => {
        logError('Request Error:', error);
        return Promise.reject(error);
      }
    );
    
    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logSuccess(`✅ ${response.status} ${response.config.method.toUpperCase()} ${response.config.url}`);
        return response;
      },
      (error) => {
        if (error.response) {
          logError(`❌ ${error.response.status} ${error.config.method.toUpperCase()} ${error.config.url}`);
          logError('Response Error:', error.response.data);
        } else {
          logError('Network Error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }
  
  // Set authorization token for requests
  setToken(userKey, token) {
    this.tokens[userKey] = token;
    log(`🔑 Token set for ${userKey}`);
  }
  
  // Get authorization headers
  getAuthHeaders(userKey) {
    const token = this.tokens[userKey];
    if (!token) {
      throw new Error(`No token found for user: ${userKey}`);
    }
    return {
      'Authorization': `Bearer ${token}`
    };
  }
  
  // Add delay to avoid rate limiting
  async delay(ms = config.RATE_LIMIT_DELAY) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Register user
  async register(userData) {
    await this.delay();
    const response = await this.client.post('/auth/register', userData);
    
    // Store token if registration successful
    if (response.data.success && response.data.data && response.data.data.token) {
      const userKey = userData.username;
      this.setToken(userKey, response.data.data.token);
    }
    
    return response;
  }
  
  // Login user
  async login(credentials) {
    await this.delay();
    const response = await this.client.post('/auth/login', credentials);
    
    // Store token if login successful
    if (response.data.success && response.data.data && response.data.data.token) {
      const userKey = credentials.email.split('@')[0]; // Use email prefix as key
      this.setToken(userKey, response.data.data.token);
    }
    
    return response;
  }
  
  // Get user profile
  async getProfile(userKey) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.get('/auth/profile', { headers });
  }
  
  // Update user profile
  async updateProfile(userKey, profileData) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.put('/auth/profile', profileData, { headers });
  }
  
  // Submit assessment
  async submitAssessment(userKey, assessmentData) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.post('/assessment/submit', assessmentData, { headers });
  }
  
  // Get assessment status
  async getAssessmentStatus(userKey, jobId) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.get(`/assessment/status/${jobId}`, { headers });
  }
  
  // Get assessment results
  async getResults(userKey, params = {}) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.get('/archive/results', { headers, params });
  }
  
  // Get specific result
  async getResult(userKey, resultId) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.get(`/archive/results/${resultId}`, { headers });
  }
  
  // Create chatbot conversation
  async createConversation(userKey, conversationData) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.post('/chatbot/conversations', conversationData, { headers });
  }
  
  // Send chatbot message
  async sendMessage(userKey, conversationId, messageData) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.post(`/chatbot/conversations/${conversationId}/messages`, messageData, { headers });
  }
  
  // Get conversations
  async getConversations(userKey, params = {}) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.get('/chatbot/conversations', { headers, params });
  }
  
  // Delete user account
  async deleteAccount(userKey) {
    await this.delay();
    const headers = this.getAuthHeaders(userKey);
    return await this.client.delete('/auth/profile', { headers });
  }
  
  // Health check
  async healthCheck(endpoint = '/health') {
    await this.delay();
    return await this.client.get(endpoint);
  }
  
  // Get token for external use (like WebSocket)
  getToken(userKey) {
    return this.tokens[userKey];
  }
}

module.exports = new ApiClient();
