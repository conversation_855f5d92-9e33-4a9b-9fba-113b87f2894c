#!/usr/bin/env node

const { logSuccess, logError, logInfo, createTestSummary, formatDuration } = require('./utils/helpers');
const config = require('./config/test-config');

// Import all test modules
const testModules = {
  '01-register': require('./tests/01-register.test'),
  '02-login': require('./tests/02-login.test'),
  '03-websocket': require('./tests/03-websocket.test'),
  '04-profile-update': require('./tests/04-profile-update.test'),
  '05-assessment': require('./tests/05-assessment.test'),
  '06-notifications': require('./tests/06-notifications.test'),
  '07-profile-persona': require('./tests/07-profile-persona.test'),
  '08-chatbot': require('./tests/08-chatbot.test'),
  '09-cleanup': require('./tests/09-cleanup.test')
};

const testDescriptions = {
  '01-register': 'User Registration Tests',
  '02-login': 'User Login Tests',
  '03-websocket': 'WebSocket Connection Tests',
  '04-profile-update': 'Profile Update Tests',
  '05-assessment': 'Assessment Submission Tests',
  '06-notifications': 'WebSocket Notification Tests',
  '07-profile-persona': 'Profile Persona Tests',
  '08-chatbot': 'Chatbot Interaction Tests',
  '09-cleanup': 'Cleanup and Account Deletion Tests'
};

function showUsage() {
  logInfo('Usage: node run-single-test.js <test-name> [user-number] [additional-params]');
  logInfo('');
  logInfo('Available tests:');
  Object.entries(testDescriptions).forEach(([key, description]) => {
    logInfo(`  ${key}: ${description}`);
  });
  logInfo('');
  logInfo('Parameters:');
  logInfo('  test-name: One of the test names above (e.g., 01-register)');
  logInfo('  user-number: Optional. 1 or 2 to test specific user only');
  logInfo('  additional-params: Optional. JSON string for additional parameters');
  logInfo('');
  logInfo('Examples:');
  logInfo('  node run-single-test.js 01-register');
  logInfo('  node run-single-test.js 02-login 1');
  logInfo('  node run-single-test.js 06-notifications null \'{"user1":"job_123","user2":"job_456"}\'');
}

async function runSingleTest(testName, userNumber = null, additionalParams = {}) {
  const startTime = Date.now();
  
  if (!testModules[testName]) {
    throw new Error(`Test '${testName}' not found. Available tests: ${Object.keys(testModules).join(', ')}`);
  }
  
  const testModule = testModules[testName];
  const testDescription = testDescriptions[testName];
  
  logSuccess(`🚀 Running Single Test: ${testDescription}`);
  logInfo(`📋 Test Configuration:`);
  logInfo(`   - Test Name: ${testName}`);
  logInfo(`   - User Number: ${userNumber || 'All users'}`);
  logInfo(`   - API Base URL: ${config.API_BASE_URL}`);
  logInfo(`   - WebSocket URL: ${config.WEBSOCKET_URL}`);
  
  if (Object.keys(additionalParams).length > 0) {
    logInfo(`   - Additional Params: ${JSON.stringify(additionalParams)}`);
  }
  
  let results = [];
  
  try {
    // Determine which function to call based on test name
    switch (testName) {
      case '01-register':
        results = await testModule.runRegistrationTests(userNumber);
        break;
        
      case '02-login':
        results = await testModule.runLoginTests(userNumber);
        break;
        
      case '03-websocket':
        results = await testModule.runWebSocketTests(userNumber);
        break;
        
      case '04-profile-update':
        results = await testModule.runProfileUpdateTests(userNumber);
        break;
        
      case '05-assessment':
        const assessmentResult = await testModule.runAssessmentTests(userNumber);
        results = assessmentResult.results;
        // Log job IDs for reference
        if (Object.keys(assessmentResult.jobIds).length > 0) {
          logInfo(`📝 Job IDs created: ${JSON.stringify(assessmentResult.jobIds)}`);
        }
        break;
        
      case '06-notifications':
        const jobIds = additionalParams.jobIds || additionalParams;
        results = await testModule.runNotificationTests(userNumber, jobIds);
        break;
        
      case '07-profile-persona':
        results = await testModule.runProfilePersonaTests(userNumber);
        break;
        
      case '08-chatbot':
        const resultIds = additionalParams.resultIds || additionalParams;
        results = await testModule.runChatbotTests(userNumber, resultIds);
        break;
        
      case '09-cleanup':
        results = await testModule.runCleanupTests(userNumber);
        break;
        
      default:
        throw new Error(`Unknown test execution path for '${testName}'`);
    }
    
  } catch (error) {
    logError(`💥 Test execution failed: ${error.message}`);
    
    // Create a failed result entry
    results = [{
      success: false,
      testName: testDescription,
      duration: Date.now() - startTime,
      message: error.message,
      error: error.message
    }];
  }
  
  // Generate summary
  const duration = Date.now() - startTime;
  const summary = createTestSummary(results);
  
  logSuccess('\n📊 TEST SUMMARY');
  logInfo('═'.repeat(50));
  logInfo(`Test: ${testDescription}`);
  logInfo(`Total Tests: ${summary.total}`);
  logInfo(`Passed: ${summary.passed}`);
  logInfo(`Failed: ${summary.failed}`);
  logInfo(`Success Rate: ${summary.successRate}`);
  logInfo(`Duration: ${formatDuration(startTime)}`);
  logInfo('═'.repeat(50));
  
  // Show individual test results
  if (results.length > 1) {
    logInfo('\n📋 Individual Results:');
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration ? ` (${result.duration}ms)` : '';
      logInfo(`${status} ${result.testName}${duration}: ${result.message}`);
    });
  }
  
  // Show failed tests details
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    logError('\n❌ Failed Tests Details:');
    failedTests.forEach(test => {
      logError(`   - ${test.testName}: ${test.message}`);
      if (test.error && typeof test.error === 'object') {
        logError(`     Error details: ${JSON.stringify(test.error, null, 2)}`);
      }
    });
  }
  
  // Success/failure message
  if (summary.passed === summary.total) {
    logSuccess(`\n🎉 All tests in '${testDescription}' passed!`);
  } else {
    logError(`\n💥 ${summary.failed} test(s) failed in '${testDescription}'.`);
  }
  
  return {
    success: summary.passed === summary.total,
    testName,
    summary,
    results,
    duration
  };
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showUsage();
    process.exit(0);
  }
  
  const testName = args[0];
  const userNumber = args[1] && args[1] !== 'null' ? parseInt(args[1]) : null;
  let additionalParams = {};
  
  if (args[2]) {
    try {
      additionalParams = JSON.parse(args[2]);
    } catch (error) {
      logError(`Invalid JSON in additional parameters: ${args[2]}`);
      logError(`Error: ${error.message}`);
      process.exit(1);
    }
  }
  
  // Validate user number
  if (userNumber !== null && (userNumber < 1 || userNumber > 2)) {
    logError('User number must be 1 or 2');
    process.exit(1);
  }
  
  try {
    const result = await runSingleTest(testName, userNumber, additionalParams);
    const exitCode = result.success ? 0 : 1;
    process.exit(exitCode);
  } catch (error) {
    logError(`Fatal error: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { runSingleTest, showUsage };
