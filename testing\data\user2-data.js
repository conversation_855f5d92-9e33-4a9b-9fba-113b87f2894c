const config = require('../config/test-config');

module.exports = {
  // Basic user data
  userData: {
    email: config.TEST_USERS.user2.email,
    username: config.TEST_USERS.user2.username,
    password: config.TEST_USERS.user2.password
  },
  
  // Profile update data
  profileUpdate: {
    username: 'testuser2_updated',
    full_name: 'Test User Two Updated',
    email: config.TEST_USERS.user2.email // Keep same email
  },
  
  // Assessment data with different variations for user 2
  assessmentData: {
    assessmentName: "User 2 Assessment Test",
    riasec: {
      realistic: 65,
      investigative: 70,
      artistic: 85,
      social: 88,
      enterprising: 75,
      conventional: 55
    },
    ocean: {
      openness: 88,
      conscientiousness: 70,
      extraversion: 85,
      agreeableness: 90,
      neuroticism: 35
    },
    viaIs: {
      creativity: 90,
      curiosity: 82,
      judgment: 72,
      loveOfLearning: 85,
      perspective: 78,
      bravery: 70,
      perseverance: 75,
      honesty: 90,
      zest: 85,
      love: 88,
      kindness: 92,
      socialIntelligence: 85,
      teamwork: 88,
      fairness: 90,
      leadership: 80,
      forgiveness: 85,
      humility: 78,
      prudence: 72,
      selfRegulation: 75,
      appreciationOfBeauty: 88,
      gratitude: 90,
      hope: 85,
      humor: 82,
      spirituality: 75
    }
  },
  
  // Chatbot conversation data
  chatbotData: {
    conversationTitle: "User 2 Creative Career Chat",
    contextType: "assessment",
    messages: [
      "Hi! I've completed my assessment and I'm excited to explore career options.",
      "I seem to have high scores in artistic and social areas. What does this mean?",
      "I'm interested in creative fields that also involve helping people.",
      "How can I combine my creativity with my desire to make a social impact?",
      "What educational paths would you recommend for my personality type?"
    ]
  },
  
  // Expected persona characteristics for user 2
  expectedPersona: {
    expectedArchetypes: [
      "The Creator",
      "The Helper",
      "The Collaborator",
      "The Inspirer"
    ],
    expectedStrengths: [
      "Creativity",
      "Social Intelligence",
      "Kindness",
      "Teamwork",
      "Communication"
    ],
    expectedCareers: [
      "Graphic Designer",
      "Social Worker",
      "Teacher",
      "Marketing Manager",
      "Counselor",
      "Art Therapist"
    ]
  },
  
  // Test metadata
  metadata: {
    userNumber: 2,
    testDescription: "Secondary test user with high artistic and social scores",
    expectedProcessingTime: "2-5 minutes",
    priority: "normal"
  }
};
