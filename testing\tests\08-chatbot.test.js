const apiClient = require('../utils/api-client');
const { logSuccess, logError, formatTestResult, validateResponse, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testCreateChatbotConversation(userData, userNumber, resultId = null) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Create Chatbot Conversation`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Prepare conversation data
    const conversationData = {
      title: userData.chatbotData.conversationTitle,
      context_type: userData.chatbotData.contextType,
      context_data: resultId ? { assessment_id: resultId } : {},
      metadata: {
        test_conversation: true,
        user_number: userNumber
      }
    };
    
    // Create conversation
    const response = await apiClient.createConversation(userKey, conversationData);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.CHATBOT_CONVERSATION_SUCCESS, [
      'data.id',
      'data.title',
      'data.context_type',
      'data.status',
      'data.created_at'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    const conversation = response.data.data;
    
    // Validate conversation data
    if (conversation.title !== conversationData.title) {
      throw new Error(`Title mismatch: expected ${conversationData.title}, got ${conversation.title}`);
    }
    
    if (conversation.context_type !== conversationData.context_type) {
      throw new Error(`Context type mismatch: expected ${conversationData.context_type}, got ${conversation.context_type}`);
    }
    
    if (conversation.status !== 'active') {
      throw new Error(`Expected status 'active', got '${conversation.status}'`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Chatbot conversation created successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Chatbot conversation created successfully',
      data: {
        conversationId: conversation.id,
        title: conversation.title,
        contextType: conversation.context_type,
        status: conversation.status,
        hasAssessmentContext: !!resultId
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function testSendChatbotMessages(userData, userNumber, conversationId) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Send Chatbot Messages`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    const messages = userData.chatbotData.messages;
    const messageResults = [];
    
    for (let i = 0; i < messages.length; i++) {
      const messageContent = messages[i];
      
      logSuccess(`💬 Sending message ${i + 1}/${messages.length}: "${messageContent.substring(0, 50)}..."`);
      
      // Send message
      const messageData = {
        content: messageContent,
        content_type: 'text',
        parent_message_id: null
      };
      
      const response = await apiClient.sendMessage(userKey, conversationId, messageData);
      
      // Validate response
      const errors = validateResponse(response, config.EXPECTED_CODES.CHATBOT_MESSAGE_SUCCESS, [
        'data.user_message.id',
        'data.user_message.content',
        'data.user_message.sender_type',
        'data.assistant_message.id',
        'data.assistant_message.content',
        'data.assistant_message.sender_type'
      ]);
      
      if (errors.length > 0) {
        throw new Error(`Message ${i + 1} validation failed: ${errors.join(', ')}`);
      }
      
      const responseData = response.data.data;
      
      // Validate user message
      if (responseData.user_message.content !== messageContent) {
        throw new Error(`User message content mismatch for message ${i + 1}`);
      }
      
      if (responseData.user_message.sender_type !== 'user') {
        throw new Error(`Expected user message sender_type 'user', got '${responseData.user_message.sender_type}'`);
      }
      
      // Validate assistant message
      if (!responseData.assistant_message.content || responseData.assistant_message.content.length === 0) {
        throw new Error(`Assistant message ${i + 1} has no content`);
      }
      
      if (responseData.assistant_message.sender_type !== 'assistant') {
        throw new Error(`Expected assistant message sender_type 'assistant', got '${responseData.assistant_message.sender_type}'`);
      }
      
      messageResults.push({
        messageNumber: i + 1,
        userMessageId: responseData.user_message.id,
        assistantMessageId: responseData.assistant_message.id,
        userContent: responseData.user_message.content,
        assistantContent: responseData.assistant_message.content.substring(0, 100) + '...',
        tokensUsed: responseData.usage?.tokens_used || 0
      });
      
      // Small delay between messages
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, `${messages.length} messages sent successfully`, duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: `${messages.length} messages sent successfully`,
      data: {
        conversationId,
        totalMessages: messages.length,
        messageResults,
        totalTokensUsed: messageResults.reduce((sum, msg) => sum + msg.tokensUsed, 0)
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function testGetConversationHistory(userData, userNumber, conversationId) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Get Conversation History`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Get conversation with messages
    const response = await apiClient.getConversations(userKey, {
      include_messages: true,
      message_limit: 50
    });
    
    // Validate response
    const errors = validateResponse(response, 200, [
      'data.conversations'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    const conversations = response.data.data.conversations;
    const targetConversation = conversations.find(conv => conv.id === conversationId);
    
    if (!targetConversation) {
      throw new Error(`Conversation ${conversationId} not found in user's conversations`);
    }
    
    // Validate conversation has messages
    if (!targetConversation.messages || targetConversation.messages.length === 0) {
      throw new Error('Conversation should have messages');
    }
    
    // Count user and assistant messages
    const userMessages = targetConversation.messages.filter(msg => msg.sender_type === 'user');
    const assistantMessages = targetConversation.messages.filter(msg => msg.sender_type === 'assistant');
    
    const expectedUserMessages = userData.chatbotData.messages.length;
    if (userMessages.length !== expectedUserMessages) {
      throw new Error(`Expected ${expectedUserMessages} user messages, found ${userMessages.length}`);
    }
    
    if (assistantMessages.length !== expectedUserMessages) {
      throw new Error(`Expected ${expectedUserMessages} assistant messages, found ${assistantMessages.length}`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Conversation history retrieved successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Conversation history retrieved successfully',
      data: {
        conversationId,
        totalMessages: targetConversation.messages.length,
        userMessages: userMessages.length,
        assistantMessages: assistantMessages.length,
        conversationTitle: targetConversation.title,
        conversationStatus: targetConversation.status
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function runChatbotTests(userNumber = null, resultIds = {}) {
  logSuccess('🤖 Starting Chatbot Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('💬 Testing User 1 Chatbot Conversation...');
    const user1ConversationResult = await testCreateChatbotConversation(user1Data, 1, resultIds.user1);
    results.push(user1ConversationResult);
    
    if (user1ConversationResult.success) {
      const conversationId = user1ConversationResult.data.conversationId;
      
      logSuccess('📝 Testing User 1 Send Chatbot Messages...');
      const user1MessagesResult = await testSendChatbotMessages(user1Data, 1, conversationId);
      results.push(user1MessagesResult);
      
      logSuccess('📋 Testing User 1 Get Conversation History...');
      const user1HistoryResult = await testGetConversationHistory(user1Data, 1, conversationId);
      results.push(user1HistoryResult);
    }
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('💬 Testing User 2 Chatbot Conversation...');
    const user2ConversationResult = await testCreateChatbotConversation(user2Data, 2, resultIds.user2);
    results.push(user2ConversationResult);
    
    if (user2ConversationResult.success) {
      const conversationId = user2ConversationResult.data.conversationId;
      
      logSuccess('📝 Testing User 2 Send Chatbot Messages...');
      const user2MessagesResult = await testSendChatbotMessages(user2Data, 2, conversationId);
      results.push(user2MessagesResult);
      
      logSuccess('📋 Testing User 2 Get Conversation History...');
      const user2HistoryResult = await testGetConversationHistory(user2Data, 2, conversationId);
      results.push(user2HistoryResult);
    }
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All chatbot tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some chatbot tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testCreateChatbotConversation,
  testSendChatbotMessages,
  testGetConversationHistory,
  runChatbotTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  const resultIds = JSON.parse(process.argv[3] || '{}');
  
  runChatbotTests(userNumber, resultIds)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in chatbot tests:', error);
      process.exit(1);
    });
}
