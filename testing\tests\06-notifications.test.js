const apiClient = require('../utils/api-client');
const websocketClient = require('../utils/websocket-client');
const { logSuccess, logError, formatTestResult, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testWebSocketNotifications(userData, userNumber, jobId) {
  const startTime = Date.now();
  const testName = `User ${userNumber} WebSocket Notifications`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Verify WebSocket is connected
    const status = websocketClient.getConnectionStatus(userKey);
    if (status !== 'connected') {
      throw new Error(`WebSocket not connected for ${userKey}. Status: ${status}`);
    }
    
    // Clear existing notifications
    websocketClient.clearNotifications(userKey);
    
    // Wait for analysis notifications
    logSuccess(`📨 Waiting for WebSocket notifications for ${userKey}...`);
    const notifications = await websocketClient.waitForAnalysisCompletion(userKey, jobId);
    
    // Validate started notification
    if (!notifications.started) {
      throw new Error('Analysis started notification not received');
    }
    
    const startedData = notifications.started.data;
    if (startedData.jobId !== jobId) {
      throw new Error(`JobId mismatch in started notification: expected ${jobId}, got ${startedData.jobId}`);
    }
    
    if (startedData.status !== 'started') {
      throw new Error(`Expected status 'started', got '${startedData.status}'`);
    }
    
    // Validate completed notification
    if (!notifications.completed) {
      throw new Error('Analysis completed notification not received');
    }
    
    const completedData = notifications.completed.data;
    if (completedData.jobId !== jobId) {
      throw new Error(`JobId mismatch in completed notification: expected ${jobId}, got ${completedData.jobId}`);
    }
    
    if (completedData.status !== 'completed') {
      throw new Error(`Expected status 'completed', got '${completedData.status}'`);
    }
    
    if (!completedData.resultId) {
      throw new Error('ResultId not provided in completed notification');
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'WebSocket notifications received successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'WebSocket notifications received successfully',
      data: {
        userKey,
        jobId,
        resultId: completedData.resultId,
        startedAt: notifications.started.timestamp,
        completedAt: notifications.completed.timestamp,
        processingTime: completedData.metadata?.processingTime
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function testNotificationHistory(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Notification History`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Get all notifications for user
    const notifications = websocketClient.getNotifications(userKey);
    
    if (notifications.length === 0) {
      throw new Error('No notifications found in history');
    }
    
    // Validate notification structure
    notifications.forEach((notification, index) => {
      if (!notification.type) {
        throw new Error(`Notification ${index} missing type`);
      }
      
      if (!notification.data) {
        throw new Error(`Notification ${index} missing data`);
      }
      
      if (!notification.timestamp) {
        throw new Error(`Notification ${index} missing timestamp`);
      }
      
      const validTypes = ['analysis-started', 'analysis-complete', 'analysis-failed'];
      if (!validTypes.includes(notification.type)) {
        throw new Error(`Invalid notification type: ${notification.type}`);
      }
    });
    
    // Check for expected notification sequence
    const startedNotifications = notifications.filter(n => n.type === 'analysis-started');
    const completedNotifications = notifications.filter(n => n.type === 'analysis-complete');
    
    if (startedNotifications.length === 0) {
      throw new Error('No analysis-started notifications found');
    }
    
    if (completedNotifications.length === 0) {
      throw new Error('No analysis-complete notifications found');
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, `Found ${notifications.length} notifications`, duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: `Found ${notifications.length} notifications`,
      data: {
        userKey,
        totalNotifications: notifications.length,
        startedCount: startedNotifications.length,
        completedCount: completedNotifications.length,
        notifications: notifications.map(n => ({
          type: n.type,
          timestamp: n.timestamp,
          jobId: n.data.jobId
        }))
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.message
    };
  }
}

async function runNotificationTests(userNumber = null, jobIds = {}) {
  logSuccess('📨 Starting WebSocket Notification Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    if (jobIds.user1) {
      logSuccess('📨 Testing User 1 WebSocket Notifications...');
      const user1NotificationResult = await testWebSocketNotifications(user1Data, 1, jobIds.user1);
      results.push(user1NotificationResult);
      
      logSuccess('📋 Testing User 1 Notification History...');
      const user1HistoryResult = await testNotificationHistory(user1Data, 1);
      results.push(user1HistoryResult);
    } else {
      logError('❌ No jobId found for User 1. Please run assessment test first.');
    }
  }
  
  if (userNumber === null || userNumber === 2) {
    if (jobIds.user2) {
      logSuccess('📨 Testing User 2 WebSocket Notifications...');
      const user2NotificationResult = await testWebSocketNotifications(user2Data, 2, jobIds.user2);
      results.push(user2NotificationResult);
      
      logSuccess('📋 Testing User 2 Notification History...');
      const user2HistoryResult = await testNotificationHistory(user2Data, 2);
      results.push(user2HistoryResult);
    } else {
      logError('❌ No jobId found for User 2. Please run assessment test first.');
    }
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All notification tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some notification tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testWebSocketNotifications,
  testNotificationHistory,
  runNotificationTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  const jobIds = JSON.parse(process.argv[3] || '{}');
  
  runNotificationTests(userNumber, jobIds)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in notification tests:', error);
      process.exit(1);
    });
}
