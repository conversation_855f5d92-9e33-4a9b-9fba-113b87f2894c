const config = require('../config/test-config');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Logging functions
function log(message, data = null) {
  if (config.LOG_LEVEL === 'debug' || config.LOG_LEVEL === 'info') {
    const timestamp = new Date().toISOString();
    const coloredMessage = config.LOG_COLORS ? 
      `${colors.cyan}[${timestamp}]${colors.reset} ${message}` : 
      `[${timestamp}] ${message}`;
    
    console.log(coloredMessage);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }
}

function logSuccess(message, data = null) {
  const timestamp = new Date().toISOString();
  const coloredMessage = config.LOG_COLORS ? 
    `${colors.green}[${timestamp}] ✅ ${message}${colors.reset}` : 
    `[${timestamp}] ✅ ${message}`;
  
  console.log(coloredMessage);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function logError(message, data = null) {
  const timestamp = new Date().toISOString();
  const coloredMessage = config.LOG_COLORS ? 
    `${colors.red}[${timestamp}] ❌ ${message}${colors.reset}` : 
    `[${timestamp}] ❌ ${message}`;
  
  console.error(coloredMessage);
  if (data) {
    console.error(JSON.stringify(data, null, 2));
  }
}

function logWarning(message, data = null) {
  const timestamp = new Date().toISOString();
  const coloredMessage = config.LOG_COLORS ? 
    `${colors.yellow}[${timestamp}] ⚠️ ${message}${colors.reset}` : 
    `[${timestamp}] ⚠️ ${message}`;
  
  console.warn(coloredMessage);
  if (data) {
    console.warn(JSON.stringify(data, null, 2));
  }
}

function logInfo(message, data = null) {
  const timestamp = new Date().toISOString();
  const coloredMessage = config.LOG_COLORS ? 
    `${colors.blue}[${timestamp}] ℹ️ ${message}${colors.reset}` : 
    `[${timestamp}] ℹ️ ${message}`;
  
  console.info(coloredMessage);
  if (data) {
    console.info(JSON.stringify(data, null, 2));
  }
}

// Test result formatting
function formatTestResult(testName, success, message, duration = null) {
  const timestamp = new Date().toISOString();
  const status = success ? '✅ PASS' : '❌ FAIL';
  const durationText = duration ? ` (${duration}ms)` : '';
  
  const coloredStatus = config.LOG_COLORS ? 
    (success ? `${colors.green}${status}${colors.reset}` : `${colors.red}${status}${colors.reset}`) : 
    status;
  
  return `[${timestamp}] ${coloredStatus} ${testName}${durationText}: ${message}`;
}

// Validation helpers
function validateResponse(response, expectedCode, requiredFields = []) {
  const errors = [];
  
  // Check status code
  if (response.status !== expectedCode) {
    errors.push(`Expected status ${expectedCode}, got ${response.status}`);
  }
  
  // Check response structure
  if (!response.data) {
    errors.push('Response data is missing');
    return errors;
  }
  
  // Check success field
  if (response.data.success !== true) {
    errors.push('Response success is not true');
  }
  
  // Check required fields
  requiredFields.forEach(field => {
    if (!hasNestedProperty(response.data, field)) {
      errors.push(`Required field '${field}' is missing`);
    }
  });
  
  return errors;
}

function hasNestedProperty(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined;
  }, obj);
}

// Retry mechanism
async function retry(fn, maxRetries = config.MAX_RETRIES, delay = config.RETRY_DELAY) {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries) {
        logWarning(`Attempt ${i + 1} failed, retrying in ${delay}ms...`, error.message);
        await sleep(delay);
        delay *= 2; // Exponential backoff
      }
    }
  }
  
  throw lastError;
}

// Sleep function
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Generate unique test ID
function generateTestId() {
  return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Format duration
function formatDuration(startTime, endTime = Date.now()) {
  const duration = endTime - startTime;
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(2)}s`;
  } else {
    return `${(duration / 60000).toFixed(2)}m`;
  }
}

// Validate assessment data structure
function validateAssessmentData(data) {
  const errors = [];
  
  // Check required top-level fields
  const requiredFields = ['assessmentName', 'riasec', 'ocean', 'viaIs'];
  requiredFields.forEach(field => {
    if (!data[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  });
  
  // Validate RIASEC scores
  if (data.riasec) {
    const riasecFields = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
    riasecFields.forEach(field => {
      if (typeof data.riasec[field] !== 'number' || data.riasec[field] < 0 || data.riasec[field] > 100) {
        errors.push(`Invalid RIASEC ${field} score: must be number between 0-100`);
      }
    });
  }
  
  // Validate OCEAN scores
  if (data.ocean) {
    const oceanFields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
    oceanFields.forEach(field => {
      if (typeof data.ocean[field] !== 'number' || data.ocean[field] < 0 || data.ocean[field] > 100) {
        errors.push(`Invalid OCEAN ${field} score: must be number between 0-100`);
      }
    });
  }
  
  return errors;
}

// Extract user key from email
function getUserKey(email) {
  return email.split('@')[0];
}

// Create test summary
function createTestSummary(results) {
  const total = results.length;
  const passed = results.filter(r => r.success).length;
  const failed = total - passed;
  const successRate = total > 0 ? ((passed / total) * 100).toFixed(2) : 0;
  
  return {
    total,
    passed,
    failed,
    successRate: `${successRate}%`,
    results
  };
}

module.exports = {
  log,
  logSuccess,
  logError,
  logWarning,
  logInfo,
  formatTestResult,
  validateResponse,
  hasNestedProperty,
  retry,
  sleep,
  generateTestId,
  formatDuration,
  validateAssessmentData,
  getUserKey,
  createTestSummary,
  colors
};
