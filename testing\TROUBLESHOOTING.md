# ATMA Backend Testing - Troubleshooting Guide

## Common Issues and Solutions

### 1. Connection Issues

#### Problem: "ECONNREFUSED" or "Network Error"
```
Error: connect ECONNREFUSED 127.0.0.1:3000
```

**Solutions:**
- Verify all ATMA services are running
- Check service ports in `config/test-config.js`
- Test service health endpoints:
  ```bash
  curl http://localhost:3000/api/health
  curl http://localhost:3001/health
  curl http://localhost:3002/health
  ```

#### Problem: WebSocket Connection Failed
```
Error: WebSocket connection failed
```

**Solutions:**
- Ensure Notification Service is running on port 3005
- Check WebSocket URL in config: `http://localhost:3000`
- Verify API Gateway is proxying WebSocket connections
- Test direct connection: `curl http://localhost:3005/health`

### 2. Authentication Issues

#### Problem: "Token required" or "Invalid token"
```
Error: Authentication failed: Token required
```

**Solutions:**
- Ensure login test runs before other tests
- Check if user registration was successful
- Verify JWT token is being stored in API client
- Check token expiration time

#### Problem: "Authentication timeout"
```
Error: Authentication timeout for testuser1
```

**Solutions:**
- Increase `WEBSOCKET_TIMEOUT` in config
- Check WebSocket authentication flow
- Verify JWT token format and payload

### 3. Assessment Issues

#### Problem: Assessment Processing Timeout
```
Error: Assessment did not complete within 300000ms
```

**Solutions:**
- Increase `ASSESSMENT_PROCESSING_TIMEOUT` in config
- Check Assessment Service logs for processing errors
- Verify AI service dependencies are running
- Check database connectivity

#### Problem: Invalid Assessment Data
```
Error: Assessment data validation failed
```

**Solutions:**
- Check assessment data structure in user data files
- Ensure all required fields are present
- Verify score ranges (0-100 for most fields)
- Check data types (numbers vs strings)

### 4. Database Issues

#### Problem: Database Connection Error
```
Error: Database connection failed
```

**Solutions:**
- Verify database is running and accessible
- Check database connection strings in services
- Ensure database migrations are applied
- Test database connectivity manually

#### Problem: User Already Exists
```
Error: User with email already exists
```

**Solutions:**
- Run cleanup test to remove existing test users
- Change test user emails in config
- Manually delete test users from database
- Use different test data

### 5. Rate Limiting Issues

#### Problem: "Rate limit exceeded"
```
Error: Too Many Requests
```

**Solutions:**
- Increase `RATE_LIMIT_DELAY` in config
- Add delays between test runs
- Check rate limiting configuration in API Gateway
- Run tests for single user instead of both

### 6. Service-Specific Issues

#### Problem: Chatbot Service Unavailable
```
Error: Chatbot service not responding
```

**Solutions:**
- Verify Chatbot Service is running on port 3006
- Check AI model dependencies
- Verify API keys for external AI services
- Test chatbot health endpoint

#### Problem: Archive Service Error
```
Error: Failed to retrieve assessment results
```

**Solutions:**
- Ensure Archive Service is running on port 3003
- Check database connectivity for Archive Service
- Verify assessment results were properly saved
- Check service logs for specific errors

### 7. Test Data Issues

#### Problem: Persona Profile Missing
```
Error: Persona profile missing from result
```

**Solutions:**
- Ensure assessment completed successfully
- Check AI processing service status
- Verify assessment data was valid
- Wait longer for processing to complete

#### Problem: WebSocket Notifications Not Received
```
Error: Analysis started notification not received
```

**Solutions:**
- Verify WebSocket connection is active
- Check Notification Service logs
- Ensure assessment was submitted successfully
- Verify user is in correct WebSocket room

## Debugging Steps

### 1. Check Service Health
```bash
# Check all services
curl http://localhost:3000/api/health

# Check individual services
curl http://localhost:3001/health  # Auth Service
curl http://localhost:3002/health  # Assessment Service
curl http://localhost:3003/health  # Archive Service
curl http://localhost:3005/health  # Notification Service
curl http://localhost:3006/health  # Chatbot Service
```

### 2. Enable Debug Logging
Edit `config/test-config.js`:
```javascript
LOG_LEVEL: 'debug',
LOG_COLORS: true
```

### 3. Run Individual Tests
```bash
# Test specific components
node run-single-test.js 01-register 1
node run-single-test.js 02-login 1
node run-single-test.js 03-websocket 1
```

### 4. Check Database State
```sql
-- Check test users
SELECT * FROM users WHERE email LIKE 'testuser%';

-- Check assessment results
SELECT * FROM analysis_results WHERE user_id IN (
  SELECT id FROM users WHERE email LIKE 'testuser%'
);

-- Check jobs
SELECT * FROM analysis_jobs WHERE user_id IN (
  SELECT id FROM users WHERE email LIKE 'testuser%'
);
```

### 5. Manual Cleanup
```bash
# Run cleanup test
node run-single-test.js 09-cleanup

# Or manual database cleanup
# DELETE FROM users WHERE email LIKE 'testuser%';
```

## Configuration Adjustments

### For Slow Systems
```javascript
// In config/test-config.js
HTTP_TIMEOUT: 60000,
WEBSOCKET_TIMEOUT: 20000,
ASSESSMENT_PROCESSING_TIMEOUT: 600000, // 10 minutes
MAX_RETRIES: 5,
RETRY_DELAY: 2000
```

### For Development Environment
```javascript
// In config/test-config.js
API_BASE_URL: 'http://localhost:3000/api',
WEBSOCKET_URL: 'http://localhost:3000',
LOG_LEVEL: 'debug',
RATE_LIMIT_DELAY: 500
```

### For Production Testing
```javascript
// In config/test-config.js
API_BASE_URL: 'https://your-domain.com/api',
WEBSOCKET_URL: 'https://your-domain.com',
LOG_LEVEL: 'info',
RATE_LIMIT_DELAY: 100
```

## Getting Help

### Log Analysis
1. Check test logs for specific error messages
2. Check service logs for backend errors
3. Check database logs for connection issues
4. Check network logs for connectivity problems

### Reporting Issues
When reporting issues, include:
1. Full error message and stack trace
2. Test configuration used
3. Service versions and status
4. Steps to reproduce the issue
5. Expected vs actual behavior

### Contact Information
- Check service documentation for specific service issues
- Review API documentation for endpoint-specific problems
- Check WebSocket manual for real-time communication issues
