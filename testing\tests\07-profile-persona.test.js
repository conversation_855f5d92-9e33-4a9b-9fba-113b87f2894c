const apiClient = require('../utils/api-client');
const { logSuccess, logError, formatTestResult, validateResponse, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testGetAssessmentResults(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Get Assessment Results`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Get assessment results list
    const response = await apiClient.getResults(userKey, { 
      limit: 10,
      status: 'completed'
    });
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.RESULTS_GET_SUCCESS, [
      'data.results',
      'data.pagination'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    const results = response.data.data.results;
    if (!Array.isArray(results) || results.length === 0) {
      throw new Error('No assessment results found');
    }
    
    // Get the most recent result
    const latestResult = results[0];
    
    // Validate result structure
    if (!latestResult.id) {
      throw new Error('Result ID missing');
    }
    
    if (!latestResult.persona_profile) {
      throw new Error('Persona profile missing from result');
    }
    
    if (latestResult.status !== 'completed') {
      throw new Error(`Expected status 'completed', got '${latestResult.status}'`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Assessment results retrieved successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Assessment results retrieved successfully',
      data: {
        resultId: latestResult.id,
        assessmentName: latestResult.assessment_name,
        status: latestResult.status,
        createdAt: latestResult.created_at,
        hasPersonaProfile: !!latestResult.persona_profile,
        totalResults: results.length
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function testGetDetailedPersonaProfile(userData, userNumber, resultId) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Get Detailed Persona Profile`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Get detailed result
    const response = await apiClient.getResult(userKey, resultId);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.RESULTS_GET_SUCCESS, [
      'data.id',
      'data.user_id',
      'data.assessment_name',
      'data.status',
      'data.assessment_data',
      'data.persona_profile'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    const result = response.data.data;
    const personaProfile = result.persona_profile;
    
    // Validate persona profile structure
    const requiredPersonaFields = [
      'archetype',
      'shortSummary',
      'strengths',
      'weaknesses',
      'careerRecommendation',
      'insights',
      'workEnvironment',
      'roleModel'
    ];
    
    requiredPersonaFields.forEach(field => {
      if (!personaProfile[field]) {
        throw new Error(`Persona profile missing required field: ${field}`);
      }
    });
    
    // Validate archetype
    if (typeof personaProfile.archetype !== 'string' || personaProfile.archetype.length === 0) {
      throw new Error('Invalid archetype in persona profile');
    }
    
    // Validate strengths and weaknesses are arrays
    if (!Array.isArray(personaProfile.strengths) || personaProfile.strengths.length === 0) {
      throw new Error('Strengths should be a non-empty array');
    }
    
    if (!Array.isArray(personaProfile.weaknesses) || personaProfile.weaknesses.length === 0) {
      throw new Error('Weaknesses should be a non-empty array');
    }
    
    // Validate career recommendations
    if (!Array.isArray(personaProfile.careerRecommendation) || personaProfile.careerRecommendation.length === 0) {
      throw new Error('Career recommendations should be a non-empty array');
    }
    
    personaProfile.careerRecommendation.forEach((career, index) => {
      if (!career.careerName) {
        throw new Error(`Career recommendation ${index} missing careerName`);
      }
      if (!career.careerProspect) {
        throw new Error(`Career recommendation ${index} missing careerProspect`);
      }
    });
    
    // Validate assessment data
    const assessmentData = result.assessment_data;
    if (!assessmentData.riasec || !assessmentData.ocean || !assessmentData.viaIs) {
      throw new Error('Assessment data missing required sections');
    }
    
    // Check if persona matches expected characteristics (if provided)
    const expectedPersona = userData.expectedPersona;
    if (expectedPersona) {
      // Check if archetype is in expected list
      if (expectedPersona.expectedArchetypes && 
          !expectedPersona.expectedArchetypes.includes(personaProfile.archetype)) {
        logError(`⚠️ Archetype '${personaProfile.archetype}' not in expected list: ${expectedPersona.expectedArchetypes.join(', ')}`);
      }
      
      // Check if some expected strengths are present
      if (expectedPersona.expectedStrengths) {
        const foundStrengths = expectedPersona.expectedStrengths.filter(strength => 
          personaProfile.strengths.some(s => s.toLowerCase().includes(strength.toLowerCase()))
        );
        if (foundStrengths.length === 0) {
          logError(`⚠️ None of expected strengths found: ${expectedPersona.expectedStrengths.join(', ')}`);
        }
      }
    }
    
    const duration = Date.now() - startTime;
    const result_formatted = formatTestResult(testName, true, 'Detailed persona profile retrieved successfully', duration);
    logSuccess(result_formatted);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Detailed persona profile retrieved successfully',
      data: {
        resultId: result.id,
        archetype: personaProfile.archetype,
        strengthsCount: personaProfile.strengths.length,
        weaknessesCount: personaProfile.weaknesses.length,
        careerRecommendationsCount: personaProfile.careerRecommendation.length,
        insightsCount: personaProfile.insights.length,
        roleModelsCount: personaProfile.roleModel.length,
        assessmentSections: Object.keys(assessmentData)
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function runProfilePersonaTests(userNumber = null) {
  logSuccess('👤 Starting Profile Persona Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('📊 Testing User 1 Get Assessment Results...');
    const user1ResultsResult = await testGetAssessmentResults(user1Data, 1);
    results.push(user1ResultsResult);
    
    if (user1ResultsResult.success) {
      logSuccess('🎭 Testing User 1 Get Detailed Persona Profile...');
      const user1PersonaResult = await testGetDetailedPersonaProfile(
        user1Data, 
        1, 
        user1ResultsResult.data.resultId
      );
      results.push(user1PersonaResult);
    }
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('📊 Testing User 2 Get Assessment Results...');
    const user2ResultsResult = await testGetAssessmentResults(user2Data, 2);
    results.push(user2ResultsResult);
    
    if (user2ResultsResult.success) {
      logSuccess('🎭 Testing User 2 Get Detailed Persona Profile...');
      const user2PersonaResult = await testGetDetailedPersonaProfile(
        user2Data, 
        2, 
        user2ResultsResult.data.resultId
      );
      results.push(user2PersonaResult);
    }
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All profile persona tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some profile persona tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testGetAssessmentResults,
  testGetDetailedPersonaProfile,
  runProfilePersonaTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  
  runProfilePersonaTests(userNumber)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in profile persona tests:', error);
      process.exit(1);
    });
}
