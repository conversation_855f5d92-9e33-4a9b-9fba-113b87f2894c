const apiClient = require('../utils/api-client');
const { logSuccess, logError, formatTestResult, validateResponse, formatDuration, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testUserLogin(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Login`;
  
  try {
    // Prepare login credentials
    const credentials = {
      email: userData.userData.email,
      password: userData.userData.password
    };
    
    // Test user login
    const response = await apiClient.login(credentials);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.LOGIN_SUCCESS, [
      'data.user.id',
      'data.user.email',
      'data.user.username',
      'data.user.user_type',
      'data.user.is_active',
      'data.user.token_balance',
      'data.token',
      'message'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    // Additional validations
    const user = response.data.data.user;
    if (user.email !== userData.userData.email) {
      throw new Error(`Email mismatch: expected ${userData.userData.email}, got ${user.email}`);
    }
    
    if (user.username !== userData.userData.username) {
      throw new Error(`Username mismatch: expected ${userData.userData.username}, got ${user.username}`);
    }
    
    if (user.user_type !== 'user') {
      throw new Error(`User type should be 'user', got ${user.user_type}`);
    }
    
    if (!user.is_active) {
      throw new Error('User should be active');
    }
    
    if (!response.data.data.token) {
      throw new Error('JWT token not provided in response');
    }
    
    // Verify token is stored in API client
    const userKey = getUserKey(userData.userData.email);
    const storedToken = apiClient.getToken(userKey);
    if (!storedToken) {
      throw new Error('Token not stored in API client');
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'User logged in successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'User logged in successfully',
      data: {
        userId: user.id,
        email: user.email,
        username: user.username,
        tokenBalance: user.token_balance,
        token: response.data.data.token,
        userKey: userKey
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function testGetProfile(userKey, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Get Profile`;
  
  try {
    // Test get profile
    const response = await apiClient.getProfile(userKey);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.PROFILE_GET_SUCCESS, [
      'data.id',
      'data.email',
      'data.username',
      'data.user_type',
      'data.is_active',
      'data.token_balance',
      'data.created_at'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Profile retrieved successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Profile retrieved successfully',
      data: response.data.data
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function runLoginTests(userNumber = null) {
  logSuccess('🔐 Starting User Login Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('🔑 Testing User 1 Login...');
    const user1LoginResult = await testUserLogin(user1Data, 1);
    results.push(user1LoginResult);
    
    if (user1LoginResult.success) {
      logSuccess('📋 Testing User 1 Get Profile...');
      const user1ProfileResult = await testGetProfile(user1LoginResult.data.userKey, 1);
      results.push(user1ProfileResult);
    }
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('🔑 Testing User 2 Login...');
    const user2LoginResult = await testUserLogin(user2Data, 2);
    results.push(user2LoginResult);
    
    if (user2LoginResult.success) {
      logSuccess('📋 Testing User 2 Get Profile...');
      const user2ProfileResult = await testGetProfile(user2LoginResult.data.userKey, 2);
      results.push(user2ProfileResult);
    }
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All login tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some login tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testUserLogin,
  testGetProfile,
  runLoginTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  
  runLoginTests(userNumber)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in login tests:', error);
      process.exit(1);
    });
}
