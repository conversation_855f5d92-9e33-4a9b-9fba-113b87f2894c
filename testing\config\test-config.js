module.exports = {
  // Base URLs
  API_BASE_URL: 'http://localhost:3000/api',
  WEBSOCKET_URL: 'http://localhost:3000',
  
  // Timeout configurations (in milliseconds)
  HTTP_TIMEOUT: 30000,
  WEBSOCKET_TIMEOUT: 10000,
  ASSESSMENT_PROCESSING_TIMEOUT: 300000, // 5 minutes
  
  // Retry configurations
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  
  // WebSocket configurations
  WEBSOCKET_OPTIONS: {
    autoConnect: false,
    transports: ['websocket', 'polling'],
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    timeout: 20000,
    forceNew: false
  },
  
  // Test data configurations
  TEST_USERS: {
    user1: {
      email: '<EMAIL>',
      username: 'testuser1',
      password: 'TestPassword123!'
    },
    user2: {
      email: '<EMAIL>',
      username: 'testuser2', 
      password: 'TestPassword123!'
    }
  },
  
  // Assessment data template
  ASSESSMENT_TEMPLATE: {
    assessmentName: "Test Assessment",
    riasec: {
      realistic: 75,
      investigative: 80,
      artistic: 65,
      social: 70,
      enterprising: 85,
      conventional: 60
    },
    ocean: {
      openness: 80,
      conscientiousness: 75,
      extraversion: 70,
      agreeableness: 85,
      neuroticism: 40
    },
    viaIs: {
      creativity: 80,
      curiosity: 85,
      judgment: 75,
      loveOfLearning: 90,
      perspective: 70,
      bravery: 65,
      perseverance: 80,
      honesty: 85,
      zest: 75,
      love: 80,
      kindness: 85,
      socialIntelligence: 75,
      teamwork: 80,
      fairness: 85,
      leadership: 70,
      forgiveness: 75,
      humility: 80,
      prudence: 75,
      selfRegulation: 80,
      appreciationOfBeauty: 70,
      gratitude: 85,
      hope: 80,
      humor: 75,
      spirituality: 60
    }
  },
  
  // Profile update template
  PROFILE_UPDATE_TEMPLATE: {
    full_name: "Test User Full Name",
    username: "updated_username"
  },
  
  // Chatbot test messages
  CHATBOT_MESSAGES: [
    "Hello, I need career guidance based on my assessment results.",
    "What career paths align with my personality type?",
    "How can I develop my identified strengths?",
    "What skills should I focus on improving?"
  ],
  
  // Logging configuration
  LOG_LEVEL: 'info', // 'debug', 'info', 'warn', 'error'
  LOG_COLORS: true,
  
  // Test environment
  ENVIRONMENT: 'development',
  
  // Expected response codes
  EXPECTED_CODES: {
    REGISTER_SUCCESS: 201,
    LOGIN_SUCCESS: 200,
    PROFILE_GET_SUCCESS: 200,
    PROFILE_UPDATE_SUCCESS: 200,
    ASSESSMENT_SUBMIT_SUCCESS: 202,
    ASSESSMENT_STATUS_SUCCESS: 200,
    RESULTS_GET_SUCCESS: 200,
    CHATBOT_CONVERSATION_SUCCESS: 201,
    CHATBOT_MESSAGE_SUCCESS: 201,
    DELETE_SUCCESS: 200
  },
  
  // Rate limiting considerations
  RATE_LIMIT_DELAY: 100, // Delay between requests to avoid rate limiting
  
  // Health check endpoints
  HEALTH_ENDPOINTS: [
    '/api/health',
    '/api/auth/health',
    '/api/assessment/health',
    '/api/archive/health',
    '/api/notifications/health',
    '/api/chatbot/health'
  ]
};
