const apiClient = require('../utils/api-client');
const { logSuccess, logError, formatTestResult, validateResponse, getUserKey } = require('../utils/helpers');
const config = require('../config/test-config');
const user1Data = require('../data/user1-data');
const user2Data = require('../data/user2-data');

async function testProfileUpdate(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Profile Update`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Get current profile first
    const currentProfileResponse = await apiClient.getProfile(userKey);
    if (!currentProfileResponse.data.success) {
      throw new Error('Failed to get current profile');
    }
    
    const currentProfile = currentProfileResponse.data.data;
    
    // Test profile update
    const updateData = userData.profileUpdate;
    const response = await apiClient.updateProfile(userKey, updateData);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.PROFILE_UPDATE_SUCCESS, [
      'data.user.id',
      'data.user.username',
      'data.user.email',
      'data.user.user_type',
      'data.user.is_active',
      'data.user.token_balance',
      'data.message'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    // Additional validations
    const updatedUser = response.data.data.user;
    
    // Check if username was updated
    if (updateData.username && updatedUser.username !== updateData.username) {
      throw new Error(`Username not updated: expected ${updateData.username}, got ${updatedUser.username}`);
    }
    
    // Check if email remains the same (should not change)
    if (updatedUser.email !== currentProfile.email) {
      throw new Error(`Email should not change: expected ${currentProfile.email}, got ${updatedUser.email}`);
    }
    
    // Check if full_name was updated (if provided)
    if (updateData.full_name && updatedUser.profile) {
      if (updatedUser.profile.full_name !== updateData.full_name) {
        throw new Error(`Full name not updated: expected ${updateData.full_name}, got ${updatedUser.profile.full_name}`);
      }
    }
    
    // Verify user ID remains the same
    if (updatedUser.id !== currentProfile.id) {
      throw new Error(`User ID should not change: expected ${currentProfile.id}, got ${updatedUser.id}`);
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Profile updated successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Profile updated successfully',
      data: {
        userId: updatedUser.id,
        oldUsername: currentProfile.username,
        newUsername: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.profile?.full_name,
        updatedFields: Object.keys(updateData)
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function testProfileGetAfterUpdate(userData, userNumber) {
  const startTime = Date.now();
  const testName = `User ${userNumber} Get Profile After Update`;
  const userKey = getUserKey(userData.userData.email);
  
  try {
    // Get updated profile
    const response = await apiClient.getProfile(userKey);
    
    // Validate response
    const errors = validateResponse(response, config.EXPECTED_CODES.PROFILE_GET_SUCCESS, [
      'data.id',
      'data.email',
      'data.username',
      'data.user_type',
      'data.is_active',
      'data.token_balance'
    ]);
    
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
    
    const profile = response.data.data;
    const expectedUpdate = userData.profileUpdate;
    
    // Verify updates are persisted
    if (expectedUpdate.username && profile.username !== expectedUpdate.username) {
      throw new Error(`Username not persisted: expected ${expectedUpdate.username}, got ${profile.username}`);
    }
    
    if (expectedUpdate.full_name && profile.profile) {
      if (profile.profile.full_name !== expectedUpdate.full_name) {
        throw new Error(`Full name not persisted: expected ${expectedUpdate.full_name}, got ${profile.profile.full_name}`);
      }
    }
    
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, true, 'Updated profile retrieved successfully', duration);
    logSuccess(result);
    
    return {
      success: true,
      testName,
      duration,
      message: 'Updated profile retrieved successfully',
      data: {
        userId: profile.id,
        username: profile.username,
        email: profile.email,
        fullName: profile.profile?.full_name,
        tokenBalance: profile.token_balance
      }
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const result = formatTestResult(testName, false, error.message, duration);
    logError(result);
    
    return {
      success: false,
      testName,
      duration,
      message: error.message,
      error: error.response?.data || error.message
    };
  }
}

async function runProfileUpdateTests(userNumber = null) {
  logSuccess('👤 Starting Profile Update Tests');
  
  const results = [];
  
  if (userNumber === null || userNumber === 1) {
    logSuccess('✏️ Testing User 1 Profile Update...');
    const user1UpdateResult = await testProfileUpdate(user1Data, 1);
    results.push(user1UpdateResult);
    
    if (user1UpdateResult.success) {
      logSuccess('📋 Testing User 1 Get Profile After Update...');
      const user1GetResult = await testProfileGetAfterUpdate(user1Data, 1);
      results.push(user1GetResult);
    }
  }
  
  if (userNumber === null || userNumber === 2) {
    logSuccess('✏️ Testing User 2 Profile Update...');
    const user2UpdateResult = await testProfileUpdate(user2Data, 2);
    results.push(user2UpdateResult);
    
    if (user2UpdateResult.success) {
      logSuccess('📋 Testing User 2 Get Profile After Update...');
      const user2GetResult = await testProfileGetAfterUpdate(user2Data, 2);
      results.push(user2GetResult);
    }
  }
  
  // Summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  if (passed === total) {
    logSuccess(`✅ All profile update tests passed (${passed}/${total})`);
  } else {
    logError(`❌ Some profile update tests failed (${passed}/${total})`);
  }
  
  return results;
}

// Export for use in other modules
module.exports = {
  testProfileUpdate,
  testProfileGetAfterUpdate,
  runProfileUpdateTests
};

// Run if called directly
if (require.main === module) {
  const userNumber = process.argv[2] ? parseInt(process.argv[2]) : null;
  
  runProfileUpdateTests(userNumber)
    .then(results => {
      const exitCode = results.every(r => r.success) ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in profile update tests:', error);
      process.exit(1);
    });
}
