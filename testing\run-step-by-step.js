#!/usr/bin/env node

const readline = require('readline');
const { logSuccess, logError, logInfo, formatDuration } = require('./utils/helpers');
const { runSingleTest } = require('./run-single-test');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const testSteps = [
  {
    id: '01-register',
    name: 'User Registration',
    description: 'Register 2 test users to the system',
    required: true
  },
  {
    id: '02-login',
    name: 'User Login',
    description: 'Login both users and verify authentication',
    required: true
  },
  {
    id: '03-websocket',
    name: 'WebSocket Connection',
    description: 'Connect users to WebSocket for real-time notifications',
    required: false
  },
  {
    id: '04-profile-update',
    name: 'Profile Update',
    description: 'Update user profiles with new information',
    required: false
  },
  {
    id: '05-assessment',
    name: 'Assessment Submission',
    description: 'Submit assessment data and wait for processing',
    required: true
  },
  {
    id: '06-notifications',
    name: 'WebSocket Notifications',
    description: 'Receive real-time notifications via WebSocket',
    required: false,
    needsJobIds: true
  },
  {
    id: '07-profile-persona',
    name: 'Profile Persona',
    description: 'Retrieve assessment results and persona profiles',
    required: true
  },
  {
    id: '08-chatbot',
    name: 'Chatbot Testing',
    description: 'Test chatbot conversations with assessment context',
    required: false,
    needsResultIds: true
  },
  {
    id: '09-cleanup',
    name: 'Cleanup',
    description: 'Delete test accounts and clean up resources',
    required: true
  }
];

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

function showStepInfo(step, index) {
  logInfo(`\n${'='.repeat(60)}`);
  logInfo(`STEP ${index + 1}: ${step.name}`);
  logInfo(`Description: ${step.description}`);
  logInfo(`Required: ${step.required ? 'Yes' : 'No'}`);
  if (step.needsJobIds) {
    logInfo(`Note: This step requires job IDs from assessment step`);
  }
  if (step.needsResultIds) {
    logInfo(`Note: This step requires result IDs from persona step`);
  }
  logInfo(`${'='.repeat(60)}`);
}

async function runStepByStep() {
  const startTime = Date.now();
  
  logSuccess('🚀 ATMA Backend Step-by-Step Testing');
  logInfo('This will guide you through each test step with the option to skip non-required tests.');
  logInfo('You can run tests for both users or select specific users.\n');
  
  const results = [];
  let jobIds = {};
  let resultIds = {};
  
  for (let i = 0; i < testSteps.length; i++) {
    const step = testSteps[i];
    showStepInfo(step, i);
    
    // Ask if user wants to run this step
    const runStep = await askQuestion(`Do you want to run this step? (y/n/q to quit): `);
    
    if (runStep.toLowerCase() === 'q') {
      logInfo('Quitting step-by-step testing...');
      break;
    }
    
    if (runStep.toLowerCase() !== 'y') {
      if (step.required) {
        logError(`⚠️ Skipping required step. This may cause subsequent tests to fail.`);
      }
      logInfo(`⏭️ Skipping ${step.name}`);
      continue;
    }
    
    // Ask for user selection
    const userChoice = await askQuestion(`Run for which users? (1/2/both): `);
    let userNumber = null;
    
    if (userChoice === '1') {
      userNumber = 1;
    } else if (userChoice === '2') {
      userNumber = 2;
    } else if (userChoice !== 'both') {
      logInfo('Invalid choice, running for both users...');
    }
    
    // Prepare additional parameters
    let additionalParams = {};
    
    if (step.needsJobIds && Object.keys(jobIds).length > 0) {
      additionalParams = jobIds;
      logInfo(`Using job IDs: ${JSON.stringify(jobIds)}`);
    }
    
    if (step.needsResultIds && Object.keys(resultIds).length > 0) {
      additionalParams = resultIds;
      logInfo(`Using result IDs: ${JSON.stringify(resultIds)}`);
    }
    
    // Run the test
    try {
      logSuccess(`\n🏃 Running ${step.name}...`);
      const result = await runSingleTest(step.id, userNumber, additionalParams);
      
      // Store job IDs from assessment step
      if (step.id === '05-assessment' && result.results) {
        result.results.forEach(res => {
          if (res.success && res.data && res.data.jobId) {
            if (res.testName.includes('User 1')) {
              jobIds.user1 = res.data.jobId;
            } else if (res.testName.includes('User 2')) {
              jobIds.user2 = res.data.jobId;
            }
          }
        });
        
        if (Object.keys(jobIds).length > 0) {
          logInfo(`📝 Job IDs captured: ${JSON.stringify(jobIds)}`);
        }
      }
      
      // Store result IDs from persona step
      if (step.id === '07-profile-persona' && result.results) {
        result.results.forEach(res => {
          if (res.success && res.data && res.data.resultId) {
            if (res.testName.includes('User 1')) {
              resultIds.user1 = res.data.resultId;
            } else if (res.testName.includes('User 2')) {
              resultIds.user2 = res.data.resultId;
            }
          }
        });
        
        if (Object.keys(resultIds).length > 0) {
          logInfo(`📊 Result IDs captured: ${JSON.stringify(resultIds)}`);
        }
      }
      
      results.push(...result.results);
      
      if (result.success) {
        logSuccess(`✅ ${step.name} completed successfully!`);
      } else {
        logError(`❌ ${step.name} failed!`);
        
        if (step.required) {
          const continueAnyway = await askQuestion(`This was a required step. Continue anyway? (y/n): `);
          if (continueAnyway.toLowerCase() !== 'y') {
            logInfo('Stopping due to required step failure...');
            break;
          }
        }
      }
      
    } catch (error) {
      logError(`💥 Error running ${step.name}: ${error.message}`);
      
      if (step.required) {
        const continueAnyway = await askQuestion(`This was a required step. Continue anyway? (y/n): `);
        if (continueAnyway.toLowerCase() !== 'y') {
          logInfo('Stopping due to error in required step...');
          break;
        }
      }
    }
    
    // Ask if user wants to continue
    if (i < testSteps.length - 1) {
      const continueNext = await askQuestion(`\nContinue to next step? (y/n): `);
      if (continueNext.toLowerCase() !== 'y') {
        logInfo('Stopping step-by-step testing...');
        break;
      }
    }
  }
  
  // Final summary
  const duration = Date.now() - startTime;
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  logSuccess('\n📊 STEP-BY-STEP TESTING SUMMARY');
  logInfo('═'.repeat(60));
  logInfo(`Total Tests Run: ${total}`);
  logInfo(`Passed: ${passed}`);
  logInfo(`Failed: ${total - passed}`);
  logInfo(`Success Rate: ${total > 0 ? ((passed / total) * 100).toFixed(2) : 0}%`);
  logInfo(`Total Duration: ${formatDuration(startTime)}`);
  logInfo('═'.repeat(60));
  
  if (passed === total && total > 0) {
    logSuccess('🎉 All executed tests passed!');
  } else if (total === 0) {
    logInfo('ℹ️ No tests were executed.');
  } else {
    logError(`💥 ${total - passed} test(s) failed.`);
  }
  
  rl.close();
  return {
    success: passed === total && total > 0,
    results,
    duration
  };
}

// Run if called directly
if (require.main === module) {
  runStepByStep()
    .then(result => {
      const exitCode = result.success ? 0 : 1;
      process.exit(exitCode);
    })
    .catch(error => {
      logError('Fatal error in step-by-step testing:', error);
      rl.close();
      process.exit(1);
    });
}

module.exports = { runStepByStep };
